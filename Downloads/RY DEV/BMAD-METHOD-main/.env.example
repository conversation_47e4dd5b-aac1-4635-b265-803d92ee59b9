# ===================================
# CRYPTO TRADING BOT CONFIGURATION
# ===================================

# Telegram Bot Settings
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_USER_ID=your_telegram_user_id_here

# Bot Settings
TEST_MODE=true
DEBUG_MODE=true
LOG_LEVEL=INFO

# Database Settings
DATABASE_PATH=crypto_bot.db
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24

# KuCoin Exchange Settings
KUCOIN_API_KEY=your_kucoin_api_key
KUCOIN_SECRET_KEY=your_kucoin_secret_key
KUCOIN_PASSPHRASE=your_kucoin_passphrase
KUCOIN_SANDBOX=true

# MEXC Exchange Settings
MEXC_API_KEY=your_mexc_api_key
MEXC_SECRET_KEY=your_mexc_secret_key
MEXC_SANDBOX=true

# Binance Exchange Settings (Optional)
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
BINANCE_TESTNET=true

# Trading Settings
DEFAULT_TRADING_ENABLED=true
DEFAULT_DAILY_LIMIT=100.00
MAX_DAILY_LIMIT=10000.00
MIN_ORDER_SIZE=5.00

# Security Settings
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60
SESSION_TIMEOUT_HOURS=24
REQUIRE_2FA=false

# Notification Settings
ENABLE_NOTIFICATIONS=true
ADMIN_NOTIFICATIONS=true
USER_NOTIFICATIONS=true
TRADE_NOTIFICATIONS=true

# AI/Analysis Settings
ENABLE_AI_ANALYSIS=false
AI_API_KEY=your_ai_api_key
MARKET_ANALYSIS_INTERVAL=300

# Webhook Settings (Optional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# Monitoring Settings
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60
PERFORMANCE_MONITORING=true

# Backup Settings
BACKUP_LOCATION=./backups/
KEEP_BACKUPS_DAYS=30
AUTO_BACKUP_ENABLED=true

# ===================================
# INSTRUCTIONS:
# ===================================
# 1. Copy this file to .env
# 2. Fill in your actual values
# 3. Never commit .env to git
# 4. Keep your API keys secure
# ===================================
