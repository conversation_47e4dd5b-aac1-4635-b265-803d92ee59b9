{"blacklist_paths": ["tests/", "__pycache__/", "venv/", "examples/", "tmp/", "old_versions/", "notebooks/", "scripts/", ".git/", ".vscode/", ".idea/"], "restrict_file_creation": true, "allowed_paths": ["bot/", "config/", "core/", "strategies/", "exchanges/", "analysis/", "indicators/", "risk_management/", "notifications/", "data/", "logs/", "utils/", "main.py", "settings.py", "README.md", ".env.example"], "language": "nederlands", "locale": "nl-NL", "response_language": "nederlands", "communication_language": "nederlands", "ai_language": "nl", "ai_response_language": "nederlands", "ai_communication_language": "nederlands", "project_type": "trading_bot", "project_language": "python", "documentation_language": "nederlands", "trading_settings": {"exchanges": ["kucoin", "mexc", "binance"], "default_exchange": "kucoin", "strategies": ["trend_following", "mean_reversion", "momentum"], "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"], "default_timeframe": "1h", "pairs": ["BTC/USDT", "ETH/USDT", "ADA/USDT", "XRP/USDT", "DOGE/USDT"]}, "code_standards": {"max_line_length": 100, "indentation": "spaces", "spaces_per_indent": 4, "docstring_style": "google", "typing": true, "enforce_pep8": true}, "dependencies": {"manager": "pip", "file": "requirements.txt", "virtual_env": true, "python_version": "3.13"}, "project_structure": {"modular": true, "config_driven": true, "plugin_support": true}, "security": {"encrypt_api_keys": true, "use_environment_variables": true, "sanitize_inputs": true}, "instructions": "Gebruik bestaande bestanden en mappen. Vermijd het aanmaken van nieuwe directories. Schrijf in het Nederlands. Code output moet altijd in bestaande modules geplaatst worden. Geen duplicaten of alternatieve versies van al bestaande modules aanmaken. Focus op modulaire, goed gedocumenteerde code die voldoet aan Python best practices. Zorg dat alle functionaliteit volledig getest kan worden."}