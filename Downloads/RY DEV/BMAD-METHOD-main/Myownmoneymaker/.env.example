# Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
ENCRYPTION_KEY=your_encryption_key_here

# =============================================================================
# TELEGRAM BOT CONFIGURATION
# =============================================================================
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_ADMIN_USER_ID=your_telegram_user_id_here

# =============================================================================
# EXCHANGE API CONFIGURATIONS
# =============================================================================

# KuCoin Exchange
KUCOIN_API_KEY=your_kucoin_api_key_here
KUCOIN_SECRET_KEY=your_kucoin_secret_key_here
KUCOIN_PASSPHRASE=your_kucoin_passphrase_here
KUCOIN_SANDBOX=true

# MEXC Exchange
MEXC_API_KEY=your_mexc_api_key_here
MEXC_SECRET_KEY=your_mexc_secret_key_here
MEXC_SANDBOX=true

# Binance Exchange (Optional)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_secret_key_here
BINANCE_SANDBOX=true

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# General Trading Settings
EXCHANGE=kucoin
USE_TESTNET=true
LIVE_MODE=false
TRADING_INTERVAL=60
ERROR_RETRY_DELAY=30
MAX_TRADES_PER_DAY=10

# Trading Pairs
TRADING_PAIRS=BTC/USDT,ETH/USDT,ADA/USDT
MIN_VOLUME_24H=1000000

# =============================================================================
# RISK MANAGEMENT
# =============================================================================
RISK_PER_TRADE=0.02
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENT=0.05
TAKE_PROFIT_PERCENT=0.15
MAX_DRAWDOWN=0.20
MAX_DAILY_TRADES=10
MAX_OPEN_POSITIONS=5
POSITION_SIZING_METHOD=risk_based
MAX_DAILY_LOSS=0.05

# Trailing Stops
TRAILING_STOP_ENABLED=false
TRAILING_STOP_PERCENT=0.02

# Position Scaling
POSITION_SCALING_ENABLED=false
SCALE_IN_LEVELS=0.01,0.02,0.03
SCALE_OUT_LEVELS=0.02,0.03,0.05

# =============================================================================
# TECHNICAL ANALYSIS
# =============================================================================
ADVANCED_INDICATORS_ENABLED=true
CUSTOM_INDICATORS=RSI,MACD,Bollinger,Ichimoku,Elliott
INDICATOR_TIMEFRAMES=1h,4h,1d

# =============================================================================
# AI & ML MODEL SETTINGS
# =============================================================================
ML_MODELS_ENABLED=true
MODEL_TYPE=LSTM
PREDICTION_HORIZON=24
RETRAINING_INTERVAL=168

# Multi-Model Configuration
MULTI_MODEL_ENABLED=true
MODELS=GPT4,CLAUDE,GEMINI,LLAMA
CONSENSUS_THRESHOLD=0.75

# =============================================================================
# SENTIMENT ANALYSIS
# =============================================================================
SENTIMENT_ANALYSIS_ENABLED=true
SENTIMENT_WEIGHT=0.3

# Twitter API (Optional)
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# News API (Optional)
NEWS_API_KEY=your_news_api_key_here
NEWS_MAX_LENGTH=150

# =============================================================================
# ON-CHAIN & BLOCKCHAIN ANALYSIS
# =============================================================================
ONCHAIN_ANALYSIS_ENABLED=true
ETHERSCAN_API_KEY=your_etherscan_api_key_here
WHALE_ALERT_THRESHOLD=1000000

# =============================================================================
# ECONOMIC DATA & CORRELATION
# =============================================================================
CORRELATION_ANALYSIS_ENABLED=true
CORRELATED_ASSETS=BTC,ETH,TOTAL_MARKET_CAP,DXY,SPX
CORRELATION_THRESHOLD=0.7

ECONOMIC_INDICATORS_ENABLED=true
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
FRED_API_KEY=your_fred_api_key_here
MONITORED_INDICATORS=CPI,UNEMPLOYMENT,FED_RATE,GDP

# =============================================================================
# ANOMALY DETECTION
# =============================================================================
ANOMALY_DETECTION_ENABLED=true
ANOMALY_SENSITIVITY=0.8
ANOMALY_RESPONSE=alert

# =============================================================================
# DATABASE & LOGGING
# =============================================================================
DATABASE_URL=sqlite:///trading_bot.db
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log

# =============================================================================
# ADDITIONAL SETTINGS
# =============================================================================
API_RATE_LIMIT=10
MAX_LOGIN_ATTEMPTS=3
SESSION_TIMEOUT=3600
ENABLE_2FA=false
# =============================================================================
# NOTIFICATION SYSTEM
# =============================================================================
ENABLE_NOTIFICATIONS=true
ADMIN_NOTIFICATIONS=true
USER_NOTIFICATIONS=true
TRADE_NOTIFICATIONS=true
ERROR_NOTIFICATIONS=true

# Webhook Settings (Optional)
WEBHOOK_URL=
WEBHOOK_SECRET=

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60
PERFORMANCE_MONITORING=true
HEARTBEAT_ENABLED=true
HEARTBEAT_INTERVAL=30

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
BACKUP_LOCATION=./backups/
KEEP_BACKUPS_DAYS=30
AUTO_BACKUP_ENABLED=true

# ===================================
# INSTRUCTIONS:
# ===================================
# 1. Copy this file to .env
# 2. Fill in your actual values
# 3. Never commit .env to git
# 4. Keep your API keys secure
# ===================================
