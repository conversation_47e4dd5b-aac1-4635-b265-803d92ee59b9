# 🤖 MyOwnMoneyMaker Trading Bot

Een geavanceerde cryptocurrency trading bot met AI-analyse, multi-exchange ondersteuning en Telegram interface.

## 🚀 Quick Start

### 1. Installatie
```bash
# Clone het project
git clone <repository-url>
cd Myownmoneymaker

# Installeer dependencies
pip install -r requirements.txt

# Kopieer environment template
cp .env.example .env
```

### 2. Configuratie
Bewerk `.env` met je API keys:
```bash
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_USER_ID=your_user_id_here

# Exchange API Keys
KUCOIN_API_KEY=your_kucoin_api_key
KUCOIN_SECRET_KEY=your_kucoin_secret
KUCOIN_PASSPHRASE=your_kucoin_passphrase
```

### 3. Starten
```bash
# Eenvoudige start
python main.py

# 24/7 achtergrond (aanbevolen)
./scripts/start-24-7.sh

# Met monitoring
./scripts/monitor-24-7.sh
```

## 📁 Project Structuur

```
myownmoneymaker/
├── 📄 main.py                    # Hoofdentry point
├── 📄 heartbeat_monitor.py       # 24/7 monitoring
├── 📁 bot/                       # Telegram bot interface
├── 📁 core/                      # Kernfunctionaliteit
├── 📁 exchanges/                 # Exchange integraties (KuCoin, MEXC)
├── 📁 strategies/                # Trading strategieën
├── 📁 analysis/                  # Marktanalyse & AI
├── 📁 trading/                   # Trade execution
├── 📁 database/                  # Database management
├── 📁 utils/                     # Utilities
├── 📁 config/                    # Configuratie
├── 📁 scripts/                   # Management scripts
├── 📁 docs/                      # Documentatie
├── 📁 tests/                     # Test bestanden
├── 📁 data/                      # Data opslag
└── 📁 logs/                      # Log bestanden
```

## 🎯 Kernfunctionaliteiten

### 🤖 Trading Strategieën
- **📈 Day Trading** - Intraday momentum trading
- **⚡ Scalping** - High-frequency trading (1-5 min)
- **🚀 Momentum Breakout** - Trend following
- **📊 Mean Reversion** - Oversold/overbought trading

### 🏦 Exchange Ondersteuning
- **KuCoin** - Volledig geïntegreerd
- **MEXC** - Volledig geïntegreerd
- Multi-exchange arbitrage mogelijkheden

### 🧠 AI & Analyse
- Technische indicatoren (RSI, MACD, Bollinger Bands)
- AI-powered marktanalyse
- Sentiment analyse
- Volume en orderbook analyse

### 🛡️ Risk Management
- Automatische stop-loss en take-profit
- Portfolio risk monitoring
- Daily loss limits
- Position sizing

### 📱 Telegram Interface
- Real-time trading dashboard
- Live marktdata
- Trade notifications
- Remote bot control

## 🔧 Management Scripts

```bash
# Bot beheer
./scripts/start-24-7.sh      # Start bot in achtergrond
./scripts/monitor-24-7.sh    # Live monitoring dashboard
./scripts/deploy.sh          # Production deployment

# Onderhoud
./scripts/quick_fix.sh       # Snelle fixes
./scripts/fix_ssl.sh         # SSL problemen oplossen
```

## 📊 Monitoring & Logging

- **Heartbeat Monitor** - 24/7 uptime monitoring
- **Real-time Logs** - Uitgebreide logging met Loguru
- **Performance Metrics** - Trade statistieken
- **System Health** - CPU, memory, disk monitoring

## 🐳 Docker Ondersteuning

```bash
# Docker Compose (aanbevolen)
docker-compose up -d

# Handmatige Docker build
docker build -t trading-bot .
docker run -d --env-file .env trading-bot
```

## 📚 Documentatie

- **[Setup Guide](docs/README-24-7.md)** - Uitgebreide installatie instructies
- **[Heartbeat Monitor](docs/HEARTBEAT_README.md)** - 24/7 monitoring setup
- **[Debug Guide](docs/DEBUG_PLAN.md)** - Troubleshooting

## 🧪 Testing

```bash
# Exchange verbindingen testen
python tests/test_exchange_connections.py

# Bot functionaliteit testen
python tests/comprehensive_bot_test.py

# Telegram bot testen
python tests/debug_telegram.py

# Configuratie controleren
python tests/check_config.py
```

## ⚠️ Belangrijke Opmerkingen

- **Demo Mode**: Bot start standaard in test mode
- **API Keys**: Houd je API keys veilig en deel ze nooit
- **Risk Management**: Start met kleine bedragen
- **Monitoring**: Gebruik altijd de monitoring tools

## 🆘 Support & Troubleshooting

Bij problemen:
1. Controleer `logs/bot.log`
2. Run `./scripts/monitor-24-7.sh`
3. Test met `python tests/debug_telegram.py`
4. Herstart met `./scripts/quick_fix.sh`

## 📄 Licentie

Dit project is voor educatieve doeleinden. Gebruik op eigen risico.

---

**🎉 Happy Trading!** 🚀
