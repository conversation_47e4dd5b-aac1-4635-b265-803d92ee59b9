"""
Automated Market Analysis - Runs every 5 minutes
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class MarketAnalyzer:
    """Advanced market analysis with AI recommendations"""

    def __init__(self, exchange_manager):
        self.exchange_manager = exchange_manager
        self.running = False
        self.analysis_cache = {}
        self.last_analysis = {}

        # Popular trading pairs to monitor
        self.monitored_symbols = [
            "BTC/USDT",
            "ETH/USDT",
            "BNB/USDT",
            "ADA/USDT",
            "SOL/USDT",
            "XRP/USDT",
            "DOT/USDT",
            "AVAX/USDT",
        ]

    async def start_analysis(self):
        """Start continuous market analysis"""
        self.running = True
        logger.info("📊 Market analysis started")

        while self.running:
            try:
                await self._run_analysis_cycle()
                await asyncio.sleep(60)  # Analyze every minute

            except Exception as e:
                logger.error(f"Error in analysis cycle: {e}")
                await asyncio.sleep(30)

    async def stop(self):
        """Stop market analysis"""
        self.running = False
        logger.info("🛑 Market analysis stopped")

    async def _run_analysis_cycle(self):
        """Run a complete analysis cycle"""
        try:
            for symbol in self.monitored_symbols:
                analysis = await self.analyze_symbol(symbol)
                if analysis:
                    self.analysis_cache[symbol] = analysis
                    self.last_analysis[symbol] = datetime.now()

        except Exception as e:
            logger.error(f"Error in analysis cycle: {e}")

    async def analyze_symbol(self, symbol: str) -> Dict:
        """Analyze a specific symbol"""
        try:
            # Get ticker data from all exchanges
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                return None

            # Calculate basic metrics
            prices = [float(ticker.last) for ticker in tickers.values()]
            avg_price = sum(prices) / len(prices)
            price_variance = self._calculate_price_variance(tickers)

            # Get the best ticker data
            best_ticker = list(tickers.values())[0]

            # Calculate technical indicators
            technical_analysis = self._calculate_technical_indicators(best_ticker)

            # Generate AI recommendation
            ai_recommendation = self._generate_ai_recommendation(best_ticker, technical_analysis)

            analysis = {
                "symbol": symbol,
                "price": avg_price,
                "price_variance": price_variance,
                "volume": float(best_ticker.volume),
                "change_24h": 0,  # Calculate this properly later
                "technical_analysis": technical_analysis,
                "ai_recommendation": ai_recommendation["recommendation"],
                "ai_confidence": ai_recommendation["confidence"],
                "ai_reasoning": ai_recommendation["reasoning"],
                "timestamp": datetime.now().isoformat(),
                "exchanges": list(tickers.keys()),
            }

            logger.debug(
                f"📊 Analyzed {symbol}: {ai_recommendation['recommendation']} ({ai_recommendation['confidence']:.2f})"
            )
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return None

    def _calculate_price_variance(self, tickers: Dict) -> float:
        """Calculate price variance across exchanges"""
        try:
            prices = [float(ticker.last) for ticker in tickers.values()]
            if len(prices) < 2:
                return 0.0

            avg_price = sum(prices) / len(prices)
            variance = sum((price - avg_price) ** 2 for price in prices) / len(prices)

            return (variance**0.5) / avg_price * 100  # Coefficient of variation as percentage

        except Exception:
            return 0.0

    def _calculate_technical_indicators(self, ticker) -> Dict:
        """Calculate technical indicators"""
        try:
            price = float(ticker.last)
            high = float(ticker.high)
            low = float(ticker.low)
            volume = float(ticker.volume)
            change = 0  # Calculate change properly later

            # Simple technical analysis
            indicators = {
                "price_position": (price - low) / (high - low) if high != low else 0.5,
                "volatility": (high - low) / price if price > 0 else 0,
                "volume_strength": min(volume / 1000000, 1.0),  # Normalize volume
                "momentum": max(-1, min(1, change / 10)),  # Normalize change to -1 to 1
                "support_level": low,
                "resistance_level": high,
            }

            return indicators

        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return {}

    def _generate_ai_recommendation(self, ticker, technical: Dict) -> Dict:
        """Generate AI-powered trading recommendation"""
        try:
            price = float(ticker.last)
            change = 0  # Calculate change properly later

            # Simple AI logic (can be enhanced with ML models)
            score = 0
            reasoning = []

            # Price momentum analysis
            if change > 5:
                score += 0.3
                reasoning.append("Strong positive momentum (+5%)")
            elif change > 2:
                score += 0.1
                reasoning.append("Positive momentum")
            elif change < -5:
                score -= 0.3
                reasoning.append("Strong negative momentum (-5%)")
            elif change < -2:
                score -= 0.1
                reasoning.append("Negative momentum")

            # Technical position analysis
            price_pos = technical.get("price_position", 0.5)
            if price_pos > 0.8:
                score -= 0.2
                reasoning.append("Price near resistance")
            elif price_pos < 0.2:
                score += 0.2
                reasoning.append("Price near support")

            # Volatility analysis
            volatility = technical.get("volatility", 0)
            if volatility > 0.1:
                score -= 0.1
                reasoning.append("High volatility detected")

            # Volume analysis
            volume_strength = technical.get("volume_strength", 0)
            if volume_strength > 0.7:
                score += 0.1
                reasoning.append("Strong volume")

            # Generate recommendation
            if score > 0.3:
                recommendation = "STRONG_BUY"
                confidence = min(0.95, 0.6 + abs(score))
            elif score > 0.1:
                recommendation = "BUY"
                confidence = min(0.85, 0.5 + abs(score))
            elif score < -0.3:
                recommendation = "STRONG_SELL"
                confidence = min(0.95, 0.6 + abs(score))
            elif score < -0.1:
                recommendation = "SELL"
                confidence = min(0.85, 0.5 + abs(score))
            else:
                recommendation = "HOLD"
                confidence = 0.6

            return {
                "recommendation": recommendation,
                "confidence": confidence,
                "score": score,
                "reasoning": "; ".join(reasoning) if reasoning else "Neutral market conditions",
            }

        except Exception as e:
            logger.error(f"Error generating AI recommendation: {e}")
            return {"recommendation": "HOLD", "confidence": 0.5, "score": 0, "reasoning": "Analysis error"}

    def get_analysis_summary(self) -> Dict:
        """Get summary of all analyses"""
        try:
            summary = {
                "total_symbols": len(self.analysis_cache),
                "last_update": datetime.now().isoformat(),
                "market_overview": {},
            }

            # Count recommendations
            recommendations = {}
            for symbol, analysis in self.analysis_cache.items():
                rec = analysis.get("ai_recommendation", "HOLD")
                recommendations[rec] = recommendations.get(rec, 0) + 1

                # Add to market overview
                summary["market_overview"][symbol] = {
                    "price": analysis.get("price", 0),
                    "change_24h": analysis.get("change_24h", 0),
                    "ai_recommendation": rec,
                    "ai_confidence": analysis.get("ai_confidence", 0),
                    "volume": analysis.get("volume", 0),
                }

            summary["recommendation_counts"] = recommendations
            return summary

        except Exception as e:
            logger.error(f"Error getting analysis summary: {e}")
            return {"error": str(e)}

    async def get_market_report(self) -> str:
        """Generate a formatted market report"""
        try:
            summary = self.get_analysis_summary()

            report = f"📊 **Market Analysis Report**\n"
            report += f"🕐 {summary['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            report += f"📈 **Overview:**\n"
            report += f"• Symbols analyzed: {summary['symbols_analyzed']}\n"
            report += f"• Total alerts: {summary['total_alerts']}\n"
            report += f"• High priority alerts: {summary['high_severity_alerts']}\n\n"

            report += f"💰 **Market Status:**\n"

            for symbol, data in summary["market_overview"].items():
                price_change = data["price_change_24h"]
                change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

                report += f"{change_emoji} **{symbol}**: ${data['price']:.4f} "
                report += f"({price_change:+.2f}%)\n"
                report += f"   AI: {data['ai_recommendation']} ({data['ai_confidence']:.2f})\n"

                if data["alerts"] > 0:
                    report += f"   🚨 {data['alerts']} alerts\n"
                report += "\n"

            return report

        except Exception as e:
            logger.error(f"Error generating market report: {e}")
            return "❌ Error generating market report"

    def _validate_market_data(self, market_data: Dict) -> bool:
        """Validate market data consistency"""
        required_fields = ["price", "volume", "timestamp"]

        try:
            # Check required fields
            for field in required_fields:
                if field not in market_data:
                    logger.warning(f"Missing required field: {field}")
                    return False

            # Validate price and volume
            if not isinstance(market_data["price"], (int, float)) or market_data["price"] <= 0:
                logger.warning("Invalid price value")
                return False

            if not isinstance(market_data["volume"], (int, float)) or market_data["volume"] < 0:
                logger.warning("Invalid volume value")
                return False

            # Check timestamp freshness
            if time.time() - market_data["timestamp"] > 300:  # 5 minutes
                logger.warning("Market data too old")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating market data: {e}")
            return False
