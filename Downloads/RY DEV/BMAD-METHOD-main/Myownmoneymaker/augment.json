{"blacklist_paths": ["tests/", "__pycache__/", "venv/", "examples/", "tmp/", "old_versions/", "notebooks/", "scripts/", ".git/", ".vscode/", ".idea/"], "restrict_file_creation": true, "allowed_paths": ["bot/", "config/", "core/", "strategies/", "exchanges/", "analysis/", "indicators/", "risk_management/", "notifications/", "data/", "logs/", "utils/", "main.py", "settings.py", "README.md", ".env.example"], "language": "nederlands", "locale": "nl-NL", "response_language": "nederlands", "communication_language": "nederlands", "ai_language": "nl", "ai_response_language": "nederlands", "ai_communication_language": "nederlands", "project_type": "trading_bot", "project_language": "python", "documentation_language": "nederlands", "trading_settings": {"exchanges": ["kucoin", "mexc", "binance"], "default_exchange": "kucoin", "strategies": ["trend_following", "mean_reversion", "momentum"], "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"], "default_timeframe": "1h", "pairs": ["BTC/USDT", "ETH/USDT", "ADA/USDT", "XRP/USDT", "DOGE/USDT"]}, "code_standards": {"max_line_length": 100, "indentation": "spaces", "spaces_per_indent": 4, "docstring_style": "google", "typing": true, "enforce_pep8": true}, "dependencies": {"manager": "pip", "file": "requirements.txt", "virtual_env": true, "python_version": "3.13"}, "project_structure": {"modular": true, "config_driven": true, "plugin_support": true}, "security": {"encrypt_api_keys": true, "use_environment_variables": true, "sanitize_inputs": true}, "instructions": "Gebruik uitsluitend bestaande bestanden en mappen. Maak geen nieuwe directories aan. Code moet in bestaande modules worden geplaatst, zonder duplicaten of alternatieve versies van bestaande onderdelen. Schrijf in het Nederlands. Focus op modulaire, goed gestructureerde en gedocumenteerde Python-code. Houd je aan PEP8. Alle gegenereerde functionaliteit moet testbaar zijn en logisch passen binnen de architectuur van een trading bot."}