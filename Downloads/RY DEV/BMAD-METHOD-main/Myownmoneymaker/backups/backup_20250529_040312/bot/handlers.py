"""
Telegram bot command handlers
"""
import asyncio
from decimal import Decimal, InvalidOperation
from typing import Dict, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from exchanges.manager import ExchangeManager
from config import get_settings
from loguru import logger

class TradingBotHandlers:
    """Telegram bot command handlers"""

    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager
        self.settings = get_settings()

    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized to use the bot"""
        return user_id == self.settings.telegram_admin_user_id

    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        user_id = update.effective_user.id

        if not self._is_authorized(user_id):
            await update.message.reply_text("❌ Je bent niet geautoriseerd om deze bot te gebruiken.")
            return

        welcome_message = """
🤖 **Welkom bij de Trading Bot!**

Deze bot helpt je met trading op KuCoin en MEXC exchanges.

**Beschikbare commando's:**
/balance - Toon portfolio balances
/price <symbol> - Toon prijs van een coin (bijv. /price BTC/USDT)
/buy <exchange> <symbol> <amount> [price] - Koop order
/sell <exchange> <symbol> <amount> [price] - Verkoop order
/orders - Toon open orders
/cancel <exchange> <order_id> <symbol> - Annuleer order
/exchanges - Toon beschikbare exchanges
/help - Toon deze help

**Exchanges:** kucoin, mexc

**Voorbeelden:**
• `/price BTC/USDT` - Bitcoin prijs
• `/buy kucoin BTC/USDT 0.001` - Market buy order
• `/sell mexc ETH/USDT 0.1 2000` - Limit sell order

⚠️ **Let op:** Alle trading acties zijn echt! Wees voorzichtig.
        """

        await update.message.reply_text(welcome_message, parse_mode='Markdown')

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""
        if not self._is_authorized(update.effective_user.id):
            return

        await self.start(update, context)

    async def balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Balance command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        await update.message.reply_text("💰 Balances ophalen...")

        try:
            all_balances = await self.exchange_manager.get_all_balances()

            message = "💰 **Portfolio Balances**\n\n"

            for exchange_name, balances in all_balances.items():
                message += f"**{exchange_name.upper()}:**\n"

                if not balances:
                    message += "  Geen balances gevonden\n\n"
                    continue

                # Filter out zero balances and sort by total value
                non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}

                if not non_zero_balances:
                    message += "  Geen balances > 0\n\n"
                    continue

                for currency, balance in sorted(non_zero_balances.items()):
                    message += f"  {currency}: {balance.total:.8f}\n"
                    if balance.used > 0:
                        message += f"    (Used: {balance.used:.8f})\n"

                message += "\n"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error fetching balances: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen balances: {str(e)}")

    async def price(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Price command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if not context.args:
            await update.message.reply_text("❌ Gebruik: /price <symbol>\nVoorbeeld: /price BTC/USDT")
            return

        symbol = context.args[0].upper()

        await update.message.reply_text(f"📊 Prijzen ophalen voor {symbol}...")

        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                await update.message.reply_text(f"❌ Geen prijsdata gevonden voor {symbol}")
                return

            message = f"📊 **Prijzen voor {symbol}**\n\n"

            for exchange_name, ticker in tickers.items():
                message += f"**{exchange_name.upper()}:**\n"
                message += f"  Last: ${ticker.last:.8f}\n"
                message += f"  Bid: ${ticker.bid:.8f}\n"
                message += f"  Ask: ${ticker.ask:.8f}\n"
                message += f"  24h High: ${ticker.high:.8f}\n"
                message += f"  24h Low: ${ticker.low:.8f}\n"
                message += f"  Volume: {ticker.volume:.2f}\n\n"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error fetching price for {symbol}: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen prijs: {str(e)}")

    async def exchanges(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """List available exchanges"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        exchanges = self.exchange_manager.list_exchanges()
        message = "🏦 **Beschikbare Exchanges:**\n\n"

        for exchange in exchanges:
            message += f"• {exchange}\n"

        await update.message.reply_text(message, parse_mode='Markdown')

    async def buy(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Buy command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if len(context.args) < 3:
            await update.message.reply_text(
                "❌ Gebruik: /buy <exchange> <symbol> <amount> [price]\n"
                "Voorbeelden:\n"
                "• `/buy kucoin BTC/USDT 0.001` (market order)\n"
                "• `/buy mexc ETH/USDT 0.1 2000` (limit order)"
            )
            return

        exchange_name = context.args[0].lower()
        symbol = context.args[1].upper()

        try:
            amount = Decimal(context.args[2])
            price = Decimal(context.args[3]) if len(context.args) > 3 else None
        except (InvalidOperation, IndexError):
            await update.message.reply_text("❌ Ongeldige amount of price waarde.")
            return

        if exchange_name not in self.exchange_manager.list_exchanges():
            await update.message.reply_text(f"❌ Exchange '{exchange_name}' niet gevonden.")
            return

        order_type = "limit" if price else "market"

        # Confirmation message
        confirm_msg = f"🛒 **Buy Order Bevestiging**\n\n"
        confirm_msg += f"Exchange: {exchange_name.upper()}\n"
        confirm_msg += f"Symbol: {symbol}\n"
        confirm_msg += f"Type: {order_type.upper()}\n"
        confirm_msg += f"Amount: {amount}\n"
        if price:
            confirm_msg += f"Price: ${price}\n"
            confirm_msg += f"Total: ~${amount * price:.2f}\n"
        confirm_msg += "\n⚠️ **Dit is een echte order!**"

        # Create confirmation keyboard
        keyboard = [
            [InlineKeyboardButton("✅ Bevestigen", callback_data=f"confirm_buy_{exchange_name}_{symbol}_{amount}_{price or 'market'}")],
            [InlineKeyboardButton("❌ Annuleren", callback_data="cancel_order")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(confirm_msg, parse_mode='Markdown', reply_markup=reply_markup)

    async def sell(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Sell command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if len(context.args) < 3:
            await update.message.reply_text(
                "❌ Gebruik: /sell <exchange> <symbol> <amount> [price]\n"
                "Voorbeelden:\n"
                "• `/sell kucoin BTC/USDT 0.001` (market order)\n"
                "• `/sell mexc ETH/USDT 0.1 2000` (limit order)"
            )
            return

        exchange_name = context.args[0].lower()
        symbol = context.args[1].upper()

        try:
            amount = Decimal(context.args[2])
            price = Decimal(context.args[3]) if len(context.args) > 3 else None
        except (InvalidOperation, IndexError):
            await update.message.reply_text("❌ Ongeldige amount of price waarde.")
            return

        if exchange_name not in self.exchange_manager.list_exchanges():
            await update.message.reply_text(f"❌ Exchange '{exchange_name}' niet gevonden.")
            return

        order_type = "limit" if price else "market"

        # Confirmation message
        confirm_msg = f"💰 **Sell Order Bevestiging**\n\n"
        confirm_msg += f"Exchange: {exchange_name.upper()}\n"
        confirm_msg += f"Symbol: {symbol}\n"
        confirm_msg += f"Type: {order_type.upper()}\n"
        confirm_msg += f"Amount: {amount}\n"
        if price:
            confirm_msg += f"Price: ${price}\n"
            confirm_msg += f"Total: ~${amount * price:.2f}\n"
        confirm_msg += "\n⚠️ **Dit is een echte order!**"

        # Create confirmation keyboard
        keyboard = [
            [InlineKeyboardButton("✅ Bevestigen", callback_data=f"confirm_sell_{exchange_name}_{symbol}_{amount}_{price or 'market'}")],
            [InlineKeyboardButton("❌ Annuleren", callback_data="cancel_order")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(confirm_msg, parse_mode='Markdown', reply_markup=reply_markup)

    async def orders(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show open orders"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        await update.message.reply_text("📋 Open orders ophalen...")

        try:
            all_orders = await self.exchange_manager.get_all_open_orders()

            message = "📋 **Open Orders**\n\n"

            total_orders = 0
            for exchange_name, orders in all_orders.items():
                if orders:
                    message += f"**{exchange_name.upper()}:**\n"
                    for order in orders:
                        total_orders += 1
                        message += f"• {order.side.upper()} {order.symbol}\n"
                        message += f"  ID: `{order.id}`\n"
                        message += f"  Amount: {order.amount}\n"
                        if order.price > 0:
                            message += f"  Price: ${order.price}\n"
                        message += f"  Status: {order.status}\n"
                        message += f"  Filled: {order.filled}/{order.amount}\n\n"

            if total_orders == 0:
                message += "Geen open orders gevonden."

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error fetching orders: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen orders: {str(e)}")

    async def cancel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Cancel order command"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if len(context.args) < 3:
            await update.message.reply_text(
                "❌ Gebruik: /cancel <exchange> <order_id> <symbol>\n"
                "Voorbeeld: /cancel kucoin 12345 BTC/USDT"
            )
            return

        exchange_name = context.args[0].lower()
        order_id = context.args[1]
        symbol = context.args[2].upper()

        if exchange_name not in self.exchange_manager.list_exchanges():
            await update.message.reply_text(f"❌ Exchange '{exchange_name}' niet gevonden.")
            return

        try:
            success = await self.exchange_manager.cancel_order(exchange_name, order_id, symbol)

            if success:
                await update.message.reply_text(
                    f"✅ Order {order_id} succesvol geannuleerd op {exchange_name.upper()}"
                )
            else:
                await update.message.reply_text(
                    f"❌ Kon order {order_id} niet annuleren op {exchange_name.upper()}"
                )

        except Exception as e:
            logger.error(f"Error cancelling order: {e}")
            await update.message.reply_text(f"❌ Fout bij annuleren order: {str(e)}")

    async def best_price(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Find best price across exchanges"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if len(context.args) < 2:
            await update.message.reply_text(
                "❌ Gebruik: /bestprice <symbol> <buy|sell>\n"
                "Voorbeeld: /bestprice BTC/USDT buy"
            )
            return

        symbol = context.args[0].upper()
        side = context.args[1].lower()

        if side not in ['buy', 'sell']:
            await update.message.reply_text("❌ Side moet 'buy' of 'sell' zijn.")
            return

        try:
            result = await self.exchange_manager.find_best_price(symbol, side)

            if not result:
                await update.message.reply_text(f"❌ Geen prijsdata gevonden voor {symbol}")
                return

            best_exchange, best_price = result

            message = f"🎯 **Beste Prijs voor {symbol}**\n\n"
            message += f"Voor {side.upper()}: **{best_exchange.upper()}**\n"
            message += f"Prijs: **${best_price:.8f}**\n\n"

            # Show all prices for comparison
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            message += "**Vergelijking:**\n"

            for exchange_name, ticker in tickers.items():
                price = ticker.ask if side == 'buy' else ticker.bid
                indicator = "🟢" if exchange_name == best_exchange else "⚪"
                message += f"{indicator} {exchange_name.upper()}: ${price:.8f}\n"

            await update.message.reply_text(message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error finding best price: {e}")
            await update.message.reply_text(f"❌ Fout bij zoeken beste prijs: {str(e)}")

    async def market_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show latest market analysis"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        try:
            # Get market analysis from analyzer
            if hasattr(self, 'market_analyzer'):
                report = await self.market_analyzer.get_market_report()
                await update.message.reply_text(report, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ Marktanalyse niet beschikbaar.")

        except Exception as e:
            logger.error(f"Error getting market analysis: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen marktanalyse: {str(e)}")

    async def strategies(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show trading strategies status"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        try:
            if hasattr(self, 'strategy_manager'):
                status = self.strategy_manager.get_strategy_status()

                message = "🤖 **Trading Strategieën**\n\n"

                for name, info in status.items():
                    status_emoji = "✅" if info['enabled'] else "❌"
                    active_emoji = "🟢" if info['active'] else "🔴"

                    message += f"{status_emoji} **{info.get('name', name)}**\n"
                    message += f"   Status: {active_emoji} {'Actief' if info['active'] else 'Inactief'}\n"
                    message += f"   Posities: {info['positions']}\n"
                    message += f"   Timeframe: {info['timeframe']}\n"
                    message += f"   Risico: {info['risk_percentage']}%\n"
                    message += f"   Symbolen: {', '.join(info['symbols'][:3])}\n\n"

                await update.message.reply_text(message, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")

        except Exception as e:
            logger.error(f"Error getting strategies: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen strategieën: {str(e)}")

    async def positions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show active positions"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        try:
            if hasattr(self, 'strategy_manager'):
                positions = self.strategy_manager.get_all_positions()

                if not positions:
                    await update.message.reply_text("📊 Geen actieve posities.")
                    return

                message = "📊 **Actieve Posities**\n\n"

                for position_key, position in positions.items():
                    strategy_name = position_key.split('_')[0]
                    pnl_emoji = "📈" if position.pnl_percentage > 0 else "📉" if position.pnl_percentage < 0 else "➡️"

                    message += f"**{position.symbol}** ({strategy_name})\n"
                    message += f"   Side: {position.side.upper()}\n"
                    message += f"   Entry: ${position.entry_price:.4f}\n"
                    message += f"   Current: ${position.current_price:.4f}\n"
                    message += f"   {pnl_emoji} PnL: {position.pnl_percentage:+.2f}%\n"
                    if position.stop_loss:
                        message += f"   Stop Loss: ${position.stop_loss:.4f}\n"
                    if position.take_profit:
                        message += f"   Take Profit: ${position.take_profit:.4f}\n"
                    message += "\n"

                await update.message.reply_text(message, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen posities: {str(e)}")

    async def start_auto_trading(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start automated trading"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        try:
            if hasattr(self, 'strategy_manager'):
                # Start automated trading in background
                asyncio.create_task(self.strategy_manager.start_automated_trading())
                await update.message.reply_text("🤖 Automatische trading gestart!")
            else:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")

        except Exception as e:
            logger.error(f"Error starting auto trading: {e}")
            await update.message.reply_text(f"❌ Fout bij starten auto trading: {str(e)}")

    async def stop_auto_trading(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Stop automated trading"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        try:
            if hasattr(self, 'strategy_manager'):
                self.strategy_manager.stop_automated_trading()
                await update.message.reply_text("🛑 Automatische trading gestopt!")
            else:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")

        except Exception as e:
            logger.error(f"Error stopping auto trading: {e}")
            await update.message.reply_text(f"❌ Fout bij stoppen auto trading: {str(e)}")

    async def daytrade(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start day trading with specified amount"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return

        if not context.args:
            await update.message.reply_text(
                "❌ Gebruik: /daytrade <bedrag>\n"
                "Voorbeeld: /daytrade 100\n"
                "Dit start day trading met $100"
            )
            return

        try:
            amount = Decimal(context.args[0])

            if amount <= 0:
                await update.message.reply_text("❌ Bedrag moet groter zijn dan 0.")
                return

            # Confirmation message
            confirm_msg = f"🎯 **Day Trading Starten**\n\n"
            confirm_msg += f"Bedrag: ${amount}\n"
            confirm_msg += f"Strategieën: Day Trading + AI\n"
            confirm_msg += f"Auto Stop-Loss: ✅\n"
            confirm_msg += f"Auto Trailing Stop: ✅\n\n"
            confirm_msg += f"⚠️ **Dit start automatische trading!**"

            # Create confirmation keyboard
            keyboard = [
                [InlineKeyboardButton("✅ Start Day Trading", callback_data=f"start_daytrade_{amount}")],
                [InlineKeyboardButton("❌ Annuleren", callback_data="cancel_order")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(confirm_msg, parse_mode='Markdown', reply_markup=reply_markup)

        except (ValueError, InvalidOperation):
            await update.message.reply_text("❌ Ongeldig bedrag.")
        except Exception as e:
            logger.error(f"Error in daytrade command: {e}")
            await update.message.reply_text(f"❌ Fout: {str(e)}")