"""
AI Trading Agent Controller with Risk-Based Activation
"""
import asyncio
import time
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from loguru import logger

class AgentStatus(Enum):
    """Agent status enumeration"""
    INACTIVE = "inactive"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    PAUSED = "paused"
    EMERGENCY_STOP = "emergency_stop"
    ERROR = "error"

@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    status: AgentStatus
    uptime: timedelta
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_pnl: Decimal
    daily_pnl: Decimal
    win_rate: float
    avg_trade_duration: timedelta
    last_trade_time: Optional[datetime]
    risk_score: float
    confidence_level: float

class TradingAgentController:
    """Central controller for the AI trading agent"""
    
    def __init__(self, exchange_manager, strategy_manager, risk_manager):
        self.exchange_manager = exchange_manager
        self.strategy_manager = strategy_manager
        self.risk_manager = risk_manager
        
        # Agent state
        self.status = AgentStatus.INACTIVE
        self.start_time = None
        self.last_health_check = 0
        self.health_check_interval = 30  # seconds
        
        # Performance tracking
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0
        self.trade_history = []
        self.daily_pnl = Decimal('0')
        self.total_pnl = Decimal('0')
        
        # Risk and confidence
        self.risk_score = 0.0  # 0-100, higher = more risky
        self.confidence_level = 0.0  # 0-100, higher = more confident
        
        # Auto-management settings
        self.auto_restart_on_error = True
        self.max_consecutive_failures = 5
        self.consecutive_failures = 0
        
    async def activate_agent(self, force: bool = False) -> Tuple[bool, str]:
        """Activate the trading agent with safety checks"""
        try:
            logger.info("🤖 Activating trading agent...")
            
            # Check if already active
            if self.status == AgentStatus.ACTIVE and not force:
                return True, "✅ Agent already active"
                
            # Set status to initializing
            self.status = AgentStatus.INITIALIZING
            
            # Perform safety checks
            safety_check, safety_message = await self._perform_safety_checks()
            if not safety_check and not force:
                self.status = AgentStatus.ERROR
                return False, f"❌ Safety check failed: {safety_message}"
                
            # Initialize risk manager
            if not hasattr(self.risk_manager, 'daily_start_balance'):
                await self.risk_manager._initialize_daily_tracking()
                
            # Check exchange connections
            connection_results = await self.exchange_manager.connect_all()
            connected_exchanges = [name for name, connected in connection_results.items() if connected]
            
            if not connected_exchanges:
                self.status = AgentStatus.ERROR
                return False, "❌ No exchange connections available"
                
            # Initialize strategies
            if not self.strategy_manager.strategies:
                self.strategy_manager._initialize_strategies()
                
            # Start automated trading
            if not self.strategy_manager.running:
                asyncio.create_task(self.strategy_manager.start_automated_trading())
                
            # Start health monitoring
            asyncio.create_task(self._health_monitor_loop())
            
            # Set agent as active
            self.status = AgentStatus.ACTIVE
            self.start_time = datetime.now()
            self.consecutive_failures = 0
            
            logger.info("✅ Trading agent activated successfully")
            return True, f"✅ Agent activated with {len(connected_exchanges)} exchanges"
            
        except Exception as e:
            logger.error(f"Error activating agent: {e}")
            self.status = AgentStatus.ERROR
            return False, f"❌ Activation error: {str(e)}"
            
    async def deactivate_agent(self, reason: str = "Manual stop") -> Tuple[bool, str]:
        """Deactivate the trading agent"""
        try:
            logger.info(f"🛑 Deactivating trading agent: {reason}")
            
            # Stop strategy manager
            if hasattr(self.strategy_manager, 'stop_automated_trading'):
                self.strategy_manager.stop_automated_trading()
                
            # Close all open positions (optional, based on settings)
            # await self._close_all_positions()
            
            # Update status
            self.status = AgentStatus.INACTIVE
            
            logger.info("✅ Trading agent deactivated")
            return True, f"✅ Agent deactivated: {reason}"
            
        except Exception as e:
            logger.error(f"Error deactivating agent: {e}")
            return False, f"❌ Deactivation error: {str(e)}"
            
    async def emergency_stop(self, reason: str = "Emergency condition") -> Tuple[bool, str]:
        """Emergency stop with immediate position closure"""
        try:
            logger.critical(f"🚨 EMERGENCY STOP: {reason}")
            
            # Set emergency status
            self.status = AgentStatus.EMERGENCY_STOP
            
            # Stop all trading immediately
            self.strategy_manager.stop_automated_trading()
            
            # Trigger risk manager emergency stop
            self.risk_manager.emergency_stop = True
            
            # Close all positions immediately
            await self._close_all_positions()
            
            logger.critical("🚨 Emergency stop completed")
            return True, f"🚨 Emergency stop executed: {reason}"
            
        except Exception as e:
            logger.error(f"Error in emergency stop: {e}")
            return False, f"❌ Emergency stop error: {str(e)}"
            
    async def _perform_safety_checks(self) -> Tuple[bool, str]:
        """Perform comprehensive safety checks before activation"""
        try:
            # Check risk manager emergency conditions
            emergency_check = await self.risk_manager.check_emergency_conditions()
            if emergency_check:
                return False, "Risk manager emergency conditions detected"
                
            # Check minimum balance
            total_balance = await self.risk_manager._get_total_balance()
            if total_balance < Decimal('10'):  # Minimum $10
                return False, f"Insufficient balance: ${total_balance:.2f} < $10"
                
            # Check exchange connections
            connection_results = await self.exchange_manager.connect_all()
            if not any(connection_results.values()):
                return False, "No exchange connections available"
                
            # Check strategy availability
            if not hasattr(self.strategy_manager, 'strategies') or not self.strategy_manager.strategies:
                return False, "No trading strategies available"
                
            return True, "All safety checks passed"
            
        except Exception as e:
            logger.error(f"Error in safety checks: {e}")
            return False, f"Safety check error: {str(e)}"
            
    async def _health_monitor_loop(self):
        """Continuous health monitoring loop"""
        while self.status == AgentStatus.ACTIVE:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
                
            except Exception as e:
                logger.error(f"Error in health monitor: {e}")
                
    async def _perform_health_check(self):
        """Perform health check and take corrective actions"""
        try:
            self.last_health_check = time.time()
            
            # Check risk conditions
            emergency_check = await self.risk_manager.check_emergency_conditions()
            if emergency_check:
                await self.emergency_stop("Risk manager emergency triggered")
                return
                
            # Check strategy manager status
            if not self.strategy_manager.running and self.status == AgentStatus.ACTIVE:
                logger.warning("⚠️ Strategy manager stopped, attempting restart...")
                if self.auto_restart_on_error:
                    asyncio.create_task(self.strategy_manager.start_automated_trading())
                    
            # Update performance metrics
            await self._update_performance_metrics()
            
            # Check for consecutive failures
            if self.consecutive_failures >= self.max_consecutive_failures:
                await self.emergency_stop(f"Too many consecutive failures: {self.consecutive_failures}")
                
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            self.consecutive_failures += 1
            
    async def _update_performance_metrics(self):
        """Update agent performance metrics"""
        try:
            # Get current metrics from risk manager
            risk_metrics = await self.risk_manager.get_risk_metrics()
            
            # Update daily P&L
            self.daily_pnl = risk_metrics.daily_pnl
            
            # Calculate win rate
            if self.total_trades > 0:
                win_rate = (self.successful_trades / self.total_trades) * 100
            else:
                win_rate = 0.0
                
            # Update risk score based on current conditions
            self.risk_score = min(100, max(0, float(risk_metrics.risk_percentage)))
            
            # Update confidence level based on recent performance
            if self.total_trades >= 10:
                recent_success_rate = self.successful_trades / self.total_trades
                self.confidence_level = min(100, recent_success_rate * 100)
            else:
                self.confidence_level = 50.0  # Neutral confidence for new agent
                
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
            
    async def _close_all_positions(self):
        """Close all open positions immediately"""
        try:
            if hasattr(self.strategy_manager, 'active_positions'):
                for position_key in list(self.strategy_manager.active_positions.keys()):
                    try:
                        await self.strategy_manager._close_position(position_key, "Emergency close")
                    except Exception as e:
                        logger.error(f"Error closing position {position_key}: {e}")
                        
        except Exception as e:
            logger.error(f"Error closing all positions: {e}")
            
    async def get_agent_metrics(self) -> AgentMetrics:
        """Get comprehensive agent metrics"""
        try:
            uptime = datetime.now() - self.start_time if self.start_time else timedelta(0)
            
            # Calculate average trade duration (simplified)
            avg_duration = timedelta(minutes=30)  # Default estimate
            
            # Get last trade time
            last_trade_time = None
            if self.trade_history:
                last_trade_time = max(trade.get('timestamp', datetime.min) for trade in self.trade_history)
                
            return AgentMetrics(
                status=self.status,
                uptime=uptime,
                total_trades=self.total_trades,
                successful_trades=self.successful_trades,
                failed_trades=self.failed_trades,
                total_pnl=self.total_pnl,
                daily_pnl=self.daily_pnl,
                win_rate=(self.successful_trades / self.total_trades * 100) if self.total_trades > 0 else 0.0,
                avg_trade_duration=avg_duration,
                last_trade_time=last_trade_time,
                risk_score=self.risk_score,
                confidence_level=self.confidence_level
            )
            
        except Exception as e:
            logger.error(f"Error getting agent metrics: {e}")
            return AgentMetrics(
                status=AgentStatus.ERROR,
                uptime=timedelta(0),
                total_trades=0,
                successful_trades=0,
                failed_trades=0,
                total_pnl=Decimal('0'),
                daily_pnl=Decimal('0'),
                win_rate=0.0,
                avg_trade_duration=timedelta(0),
                last_trade_time=None,
                risk_score=100.0,
                confidence_level=0.0
            )
            
    async def get_status_summary(self) -> str:
        """Get formatted status summary"""
        try:
            metrics = await self.get_agent_metrics()
            risk_summary = await self.risk_manager.get_risk_summary()
            
            status_emoji = {
                AgentStatus.INACTIVE: "⚫",
                AgentStatus.INITIALIZING: "🟡",
                AgentStatus.ACTIVE: "🟢",
                AgentStatus.PAUSED: "🟠",
                AgentStatus.EMERGENCY_STOP: "🔴",
                AgentStatus.ERROR: "❌"
            }
            
            summary = f"""
🤖 **AI Trading Agent Status**

{status_emoji.get(metrics.status, "❓")} **Status:** {metrics.status.value.upper()}
⏱️ **Uptime:** {str(metrics.uptime).split('.')[0]}
📊 **Trades:** {metrics.total_trades} (Win Rate: {metrics.win_rate:.1f}%)
💰 **Daily P&L:** ${metrics.daily_pnl:+.2f}
🎯 **Confidence:** {metrics.confidence_level:.1f}%
⚠️ **Risk Score:** {metrics.risk_score:.1f}%

{risk_summary}
            """
            
            return summary.strip()
            
        except Exception as e:
            logger.error(f"Error generating status summary: {e}")
            return "❌ Error generating agent status"
