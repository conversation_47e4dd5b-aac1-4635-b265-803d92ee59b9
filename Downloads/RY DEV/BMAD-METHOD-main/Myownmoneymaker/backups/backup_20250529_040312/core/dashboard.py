"""
Dashboard en rapportage module voor trading bot performance
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from loguru import logger
import json
from pathlib import Path

@dataclass
class TradingMetrics:
    """Trading performance metrics"""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: Decimal = Decimal('0')
    total_fees: Decimal = Decimal('0')
    max_drawdown: Decimal = Decimal('0')
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    avg_win: Decimal = Decimal('0')
    avg_loss: Decimal = Decimal('0')
    profit_factor: float = 0.0

@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot op een moment"""
    timestamp: datetime
    total_value: Decimal
    available_balance: Decimal
    positions_value: Decimal
    unrealized_pnl: Decimal
    daily_pnl: Decimal

@dataclass
class TradeRecord:
    """Individual trade record"""
    timestamp: datetime
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: Decimal
    price: Decimal
    fee: Decimal
    exchange: str
    strategy: str
    pnl: Optional[Decimal] = None
    order_id: str = ""

class TradingDashboard:
    """
    Trading dashboard voor performance tracking en rapportage

    Deze klasse verzamelt en analyseert trading data voor
    performance monitoring en rapportage.
    """

    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)

        self.trades: List[TradeRecord] = []
        self.portfolio_history: List[PortfolioSnapshot] = []
        self.daily_metrics: Dict[str, TradingMetrics] = {}

        # Load existing data
        self._load_data()

    def _load_data(self):
        """Laad bestaande trading data"""
        try:
            trades_file = self.data_dir / "trades.json"
            if trades_file.exists():
                with open(trades_file, 'r') as f:
                    trades_data = json.load(f)
                    self.trades = [
                        TradeRecord(
                            timestamp=datetime.fromisoformat(t['timestamp']),
                            symbol=t['symbol'],
                            side=t['side'],
                            amount=Decimal(str(t['amount'])),
                            price=Decimal(str(t['price'])),
                            fee=Decimal(str(t['fee'])),
                            exchange=t['exchange'],
                            strategy=t['strategy'],
                            pnl=Decimal(str(t['pnl'])) if t.get('pnl') else None,
                            order_id=t.get('order_id', '')
                        ) for t in trades_data
                    ]

            portfolio_file = self.data_dir / "portfolio_history.json"
            if portfolio_file.exists():
                with open(portfolio_file, 'r') as f:
                    portfolio_data = json.load(f)
                    self.portfolio_history = [
                        PortfolioSnapshot(
                            timestamp=datetime.fromisoformat(p['timestamp']),
                            total_value=Decimal(str(p['total_value'])),
                            available_balance=Decimal(str(p['available_balance'])),
                            positions_value=Decimal(str(p['positions_value'])),
                            unrealized_pnl=Decimal(str(p['unrealized_pnl'])),
                            daily_pnl=Decimal(str(p['daily_pnl']))
                        ) for p in portfolio_data
                    ]

            logger.info(f"✅ Loaded {len(self.trades)} trades and {len(self.portfolio_history)} portfolio snapshots")

        except Exception as e:
            logger.error(f"❌ Error loading dashboard data: {e}")

    def _save_data(self):
        """Sla trading data op"""
        try:
            # Save trades
            trades_data = [
                {
                    'timestamp': t.timestamp.isoformat(),
                    'symbol': t.symbol,
                    'side': t.side,
                    'amount': str(t.amount),
                    'price': str(t.price),
                    'fee': str(t.fee),
                    'exchange': t.exchange,
                    'strategy': t.strategy,
                    'pnl': str(t.pnl) if t.pnl else None,
                    'order_id': t.order_id
                } for t in self.trades
            ]

            with open(self.data_dir / "trades.json", 'w') as f:
                json.dump(trades_data, f, indent=2)

            # Save portfolio history
            portfolio_data = [
                {
                    'timestamp': p.timestamp.isoformat(),
                    'total_value': str(p.total_value),
                    'available_balance': str(p.available_balance),
                    'positions_value': str(p.positions_value),
                    'unrealized_pnl': str(p.unrealized_pnl),
                    'daily_pnl': str(p.daily_pnl)
                } for p in self.portfolio_history
            ]

            with open(self.data_dir / "portfolio_history.json", 'w') as f:
                json.dump(portfolio_data, f, indent=2)

        except Exception as e:
            logger.error(f"❌ Error saving dashboard data: {e}")

    def add_trade(self, trade: TradeRecord):
        """Voeg nieuwe trade toe"""
        self.trades.append(trade)
        self._save_data()
        logger.info(f"📊 Trade recorded: {trade.side.upper()} {trade.amount} {trade.symbol} @ {trade.price}")

    def add_portfolio_snapshot(self, snapshot: PortfolioSnapshot):
        """Voeg portfolio snapshot toe"""
        self.portfolio_history.append(snapshot)
        # Keep only last 1000 snapshots
        if len(self.portfolio_history) > 1000:
            self.portfolio_history = self.portfolio_history[-1000:]
        self._save_data()

    def calculate_daily_metrics(self, date: datetime = None) -> TradingMetrics:
        """Bereken dagelijkse trading metrics"""
        if date is None:
            date = datetime.now()

        # Filter trades voor de dag
        start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)

        daily_trades = [
            t for t in self.trades
            if start_of_day <= t.timestamp < end_of_day
        ]

        if not daily_trades:
            return TradingMetrics()

        # Bereken metrics
        total_trades = len(daily_trades)
        winning_trades = len([t for t in daily_trades if t.pnl and t.pnl > 0])
        losing_trades = len([t for t in daily_trades if t.pnl and t.pnl < 0])

        total_pnl = sum(t.pnl for t in daily_trades if t.pnl) or Decimal('0')
        total_fees = sum(t.fee for t in daily_trades)

        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        winning_pnl = sum(t.pnl for t in daily_trades if t.pnl and t.pnl > 0) or Decimal('0')
        losing_pnl = abs(sum(t.pnl for t in daily_trades if t.pnl and t.pnl < 0)) or Decimal('0')

        avg_win = winning_pnl / winning_trades if winning_trades > 0 else Decimal('0')
        avg_loss = losing_pnl / losing_trades if losing_trades > 0 else Decimal('0')

        profit_factor = float(winning_pnl / losing_pnl) if losing_pnl > 0 else 0

        return TradingMetrics(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            total_pnl=total_pnl,
            total_fees=total_fees,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor
        )

    def get_performance_summary(self, days: int = 7) -> Dict:
        """Krijg performance samenvatting voor laatste X dagen"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        period_trades = [
            t for t in self.trades
            if start_date <= t.timestamp <= end_date
        ]

        if not period_trades:
            return {"message": "Geen trades in de geselecteerde periode"}

        # Bereken totale metrics
        total_pnl = sum(t.pnl for t in period_trades if t.pnl) or Decimal('0')
        total_fees = sum(t.fee for t in period_trades)
        net_pnl = total_pnl - total_fees

        # Strategy performance
        strategy_performance = {}
        for trade in period_trades:
            if trade.strategy not in strategy_performance:
                strategy_performance[trade.strategy] = {
                    'trades': 0, 'pnl': Decimal('0'), 'fees': Decimal('0')
                }
            strategy_performance[trade.strategy]['trades'] += 1
            if trade.pnl:
                strategy_performance[trade.strategy]['pnl'] += trade.pnl
            strategy_performance[trade.strategy]['fees'] += trade.fee

        return {
            'period_days': days,
            'total_trades': len(period_trades),
            'total_pnl': float(total_pnl),
            'total_fees': float(total_fees),
            'net_pnl': float(net_pnl),
            'strategy_performance': {
                k: {
                    'trades': v['trades'],
                    'pnl': float(v['pnl']),
                    'fees': float(v['fees']),
                    'net': float(v['pnl'] - v['fees'])
                } for k, v in strategy_performance.items()
            }
        }

    def get_advanced_metrics(self) -> Dict[str, Any]:
        """Krijg geavanceerde trading metrics"""
        try:
            if not self.trades:
                return {"message": "Geen trading data beschikbaar"}

            # Calculate advanced metrics
            returns = []
            equity_curve = []
            running_balance = 1000.0  # Starting balance

            for trade in self.trades:
                if trade.pnl:
                    trade_return = float(trade.pnl) / running_balance
                    returns.append(trade_return)
                    running_balance += float(trade.pnl)
                    equity_curve.append(running_balance)

            if not returns:
                return {"message": "Geen P&L data beschikbaar"}

            # Calculate Sharpe ratio (simplified)
            import statistics
            avg_return = statistics.mean(returns) if returns else 0
            return_std = statistics.stdev(returns) if len(returns) > 1 else 0
            sharpe_ratio = (avg_return / return_std * (252 ** 0.5)) if return_std > 0 else 0

            # Calculate maximum drawdown
            peak = max(equity_curve) if equity_curve else 1000
            trough = min(equity_curve) if equity_curve else 1000
            max_drawdown = ((peak - trough) / peak) * 100 if peak > 0 else 0

            # Win/Loss streaks
            win_streak = 0
            loss_streak = 0
            current_win_streak = 0
            current_loss_streak = 0

            for trade in self.trades:
                if trade.pnl and trade.pnl > 0:
                    current_win_streak += 1
                    current_loss_streak = 0
                    win_streak = max(win_streak, current_win_streak)
                elif trade.pnl and trade.pnl < 0:
                    current_loss_streak += 1
                    current_win_streak = 0
                    loss_streak = max(loss_streak, current_loss_streak)

            return {
                'sharpe_ratio': round(sharpe_ratio, 3),
                'max_drawdown_percent': round(max_drawdown, 2),
                'total_return_percent': round(((running_balance - 1000) / 1000) * 100, 2),
                'max_win_streak': win_streak,
                'max_loss_streak': loss_streak,
                'total_trades': len(self.trades),
                'avg_trade_duration_hours': self._calculate_avg_trade_duration(),
                'profit_factor': self._calculate_profit_factor(),
                'equity_curve': equity_curve[-50:] if len(equity_curve) > 50 else equity_curve  # Last 50 points
            }

        except Exception as e:
            logger.error(f"❌ Error calculating advanced metrics: {e}")
            return {"error": str(e)}

    def _calculate_avg_trade_duration(self) -> float:
        """Calculate average trade duration in hours"""
        try:
            durations = []
            for i in range(1, len(self.trades)):
                duration = (self.trades[i].timestamp - self.trades[i-1].timestamp).total_seconds() / 3600
                durations.append(duration)

            return sum(durations) / len(durations) if durations else 0

        except Exception as e:
            logger.error(f"Error calculating trade duration: {e}")
            return 0

    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor (gross profit / gross loss)"""
        try:
            gross_profit = sum(float(t.pnl) for t in self.trades if t.pnl and t.pnl > 0)
            gross_loss = abs(sum(float(t.pnl) for t in self.trades if t.pnl and t.pnl < 0))

            return gross_profit / gross_loss if gross_loss > 0 else 0

        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0

    def generate_performance_report(self) -> str:
        """Generate formatted performance report"""
        try:
            metrics = self.get_advanced_metrics()

            if "message" in metrics or "error" in metrics:
                return "📊 Geen trading data beschikbaar voor rapport"

            report = f"""
📊 **ADVANCED TRADING PERFORMANCE REPORT**

📈 **Returns & Risk:**
• Total Return: {metrics['total_return_percent']:.2f}%
• Sharpe Ratio: {metrics['sharpe_ratio']:.3f}
• Max Drawdown: {metrics['max_drawdown_percent']:.2f}%
• Profit Factor: {metrics['profit_factor']:.2f}

🎯 **Trading Statistics:**
• Total Trades: {metrics['total_trades']}
• Max Win Streak: {metrics['max_win_streak']}
• Max Loss Streak: {metrics['max_loss_streak']}
• Avg Trade Duration: {metrics['avg_trade_duration_hours']:.1f}h

📅 **Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            return report

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return "❌ Error generating performance report"

# Global dashboard instance
_dashboard = None

def get_dashboard() -> TradingDashboard:
    """Get global dashboard instance"""
    global _dashboard
    if _dashboard is None:
        _dashboard = TradingDashboard()
    return _dashboard
