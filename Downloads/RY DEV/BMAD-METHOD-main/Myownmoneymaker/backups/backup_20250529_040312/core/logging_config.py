"""
Verbeterde logging configuratie voor de trading bot
"""
import sys
import os
from pathlib import Path
from loguru import logger
from typing import Optional
from config.settings import Settings

class LoggingConfig:
    """
    Centralized logging configuration voor de trading bot
    
    Deze klasse configureert logging met verschillende levels,
    file rotation, en structured logging voor betere debugging.
    """
    
    def __init__(self, settings: Optional[Settings] = None):
        self.settings = settings or Settings()
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
    def setup_logging(self):
        """Setup logging configuratie"""
        # Remove default logger
        logger.remove()
        
        # Console logging met kleuren
        logger.add(
            sys.stdout,
            level=self.settings.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # File logging voor algemene logs
        logger.add(
            self.log_dir / "trading_bot.log",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # Separate file voor trading activiteiten
        logger.add(
            self.log_dir / "trading.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
            filter=lambda record: "TRADE" in record["message"] or "ORDER" in record["message"],
            rotation="5 MB",
            retention="90 days",
            compression="zip"
        )
        
        # Error logging
        logger.add(
            self.log_dir / "errors.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}\n{exception}",
            rotation="5 MB",
            retention="90 days",
            compression="zip",
            backtrace=True,
            diagnose=True
        )
        
        # Performance logging
        logger.add(
            self.log_dir / "performance.log",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            filter=lambda record: "PERF" in record["message"],
            rotation="5 MB",
            retention="30 days"
        )
        
        logger.info("✅ Logging configuration initialized")
        
    def log_trade(self, action: str, symbol: str, amount: float, price: float, 
                  exchange: str, order_id: str = "", status: str = ""):
        """Log trading activiteit"""
        logger.info(
            f"TRADE | {action.upper()} | {symbol} | "
            f"Amount: {amount} | Price: {price} | "
            f"Exchange: {exchange} | Order: {order_id} | Status: {status}"
        )
        
    def log_performance(self, metric: str, value: float, context: str = ""):
        """Log performance metrics"""
        logger.info(f"PERF | {metric}: {value} | {context}")
        
    def log_risk_event(self, event_type: str, symbol: str, details: str):
        """Log risk management events"""
        logger.warning(f"RISK | {event_type} | {symbol} | {details}")
        
    def log_strategy_signal(self, strategy: str, symbol: str, signal: str, confidence: float):
        """Log strategy signals"""
        logger.info(f"SIGNAL | {strategy} | {symbol} | {signal} | Confidence: {confidence:.2f}")

# Global logging instance
_logging_config = None

def setup_logging(settings: Optional[Settings] = None):
    """Setup global logging configuration"""
    global _logging_config
    _logging_config = LoggingConfig(settings)
    _logging_config.setup_logging()
    return _logging_config

def get_logger():
    """Get configured logger instance"""
    return logger

def log_trade(action: str, symbol: str, amount: float, price: float, 
              exchange: str, order_id: str = "", status: str = ""):
    """Convenience function voor trade logging"""
    if _logging_config:
        _logging_config.log_trade(action, symbol, amount, price, exchange, order_id, status)

def log_performance(metric: str, value: float, context: str = ""):
    """Convenience function voor performance logging"""
    if _logging_config:
        _logging_config.log_performance(metric, value, context)

def log_risk_event(event_type: str, symbol: str, details: str):
    """Convenience function voor risk event logging"""
    if _logging_config:
        _logging_config.log_risk_event(event_type, symbol, details)

def log_strategy_signal(strategy: str, symbol: str, signal: str, confidence: float):
    """Convenience function voor strategy signal logging"""
    if _logging_config:
        _logging_config.log_strategy_signal(strategy, symbol, signal, confidence)
