"""
Production Setup Manager voor deployment en monitoring
"""
import os
import json
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

class ProductionManager:
    """
    Production Setup Manager voor het voorbereiden van de bot voor productie
    
    Beheert deployment, backup, monitoring, en security configuraties.
    """
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.backup_dir = self.project_root / "backups"
        self.config_dir = self.project_root / "config"
        self.logs_dir = self.project_root / "logs"
        
        # Ensure directories exist
        self.backup_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
    def check_production_readiness(self) -> Dict[str, Any]:
        """Check of het systeem klaar is voor productie"""
        try:
            checks = {
                'security': self._check_security(),
                'configuration': self._check_configuration(),
                'dependencies': self._check_dependencies(),
                'monitoring': self._check_monitoring(),
                'backup': self._check_backup_system(),
                'performance': self._check_performance_settings()
            }
            
            # Calculate overall score
            total_checks = sum(len(category) for category in checks.values())
            passed_checks = sum(
                sum(1 for check in category.values() if check.get('status') == 'pass')
                for category in checks.values()
            )
            
            overall_score = (passed_checks / total_checks * 100) if total_checks > 0 else 0
            
            return {
                'overall_score': round(overall_score, 1),
                'production_ready': overall_score >= 80,
                'checks': checks,
                'recommendations': self._get_recommendations(checks)
            }
            
        except Exception as e:
            logger.error(f"❌ Error checking production readiness: {e}")
            return {'error': str(e)}
    
    def _check_security(self) -> Dict[str, Dict]:
        """Check security configuratie"""
        checks = {}
        
        # Check .env file security
        env_file = self.project_root / ".env"
        if env_file.exists():
            with open(env_file, 'r') as f:
                env_content = f.read()
                
            checks['env_file_exists'] = {
                'status': 'pass',
                'message': '.env file found'
            }
            
            # Check for sensitive data exposure
            if 'your_token_here' in env_content or 'your_key_here' in env_content:
                checks['api_keys_configured'] = {
                    'status': 'fail',
                    'message': 'Default API keys detected - update with real credentials'
                }
            else:
                checks['api_keys_configured'] = {
                    'status': 'pass',
                    'message': 'API keys appear to be configured'
                }
        else:
            checks['env_file_exists'] = {
                'status': 'fail',
                'message': '.env file not found'
            }
        
        # Check file permissions
        sensitive_files = ['.env', 'config/settings.py']
        for file_path in sensitive_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                stat = full_path.stat()
                # Check if file is readable by others (basic check)
                if oct(stat.st_mode)[-1] in ['4', '5', '6', '7']:
                    checks[f'{file_path}_permissions'] = {
                        'status': 'warn',
                        'message': f'{file_path} may be readable by others'
                    }
                else:
                    checks[f'{file_path}_permissions'] = {
                        'status': 'pass',
                        'message': f'{file_path} permissions OK'
                    }
        
        # Check for encryption key
        encryption_key = os.getenv('ENCRYPTION_KEY', '')
        if encryption_key and len(encryption_key) >= 32:
            checks['encryption_configured'] = {
                'status': 'pass',
                'message': 'Encryption key configured'
            }
        else:
            checks['encryption_configured'] = {
                'status': 'fail',
                'message': 'Encryption key not configured or too short'
            }
        
        return checks
    
    def _check_configuration(self) -> Dict[str, Dict]:
        """Check configuratie instellingen"""
        checks = {}
        
        # Check critical environment variables
        required_vars = [
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_ADMIN_USER_ID',
            'KUCOIN_API_KEY',
            'MEXC_API_KEY'
        ]
        
        for var in required_vars:
            value = os.getenv(var, '')
            if value and value != 'your_token_here':
                checks[f'{var.lower()}_configured'] = {
                    'status': 'pass',
                    'message': f'{var} is configured'
                }
            else:
                checks[f'{var.lower()}_configured'] = {
                    'status': 'fail',
                    'message': f'{var} is not configured'
                }
        
        # Check sandbox/live mode settings
        live_mode = os.getenv('LIVE_MODE', '0').lower() in ['1', 'true', 'yes']
        use_testnet = os.getenv('USE_TESTNET', '1').lower() in ['1', 'true', 'yes']
        
        if live_mode and use_testnet:
            checks['mode_configuration'] = {
                'status': 'fail',
                'message': 'LIVE_MODE and USE_TESTNET cannot both be true'
            }
        elif live_mode:
            checks['mode_configuration'] = {
                'status': 'warn',
                'message': 'LIVE_MODE enabled - ensure this is intentional'
            }
        else:
            checks['mode_configuration'] = {
                'status': 'pass',
                'message': 'Running in safe mode (testnet/sandbox)'
            }
        
        return checks
    
    def _check_dependencies(self) -> Dict[str, Dict]:
        """Check dependencies en packages"""
        checks = {}
        
        # Check if requirements.txt exists
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            checks['requirements_file'] = {
                'status': 'pass',
                'message': 'requirements.txt found'
            }
        else:
            checks['requirements_file'] = {
                'status': 'warn',
                'message': 'requirements.txt not found'
            }
        
        # Check critical packages
        critical_packages = [
            'aiohttp', 'loguru', 'pandas', 'numpy', 'psutil'
        ]
        
        for package in critical_packages:
            try:
                __import__(package)
                checks[f'{package}_installed'] = {
                    'status': 'pass',
                    'message': f'{package} is installed'
                }
            except ImportError:
                checks[f'{package}_installed'] = {
                    'status': 'fail',
                    'message': f'{package} is not installed'
                }
        
        return checks
    
    def _check_monitoring(self) -> Dict[str, Dict]:
        """Check monitoring setup"""
        checks = {}
        
        # Check log directory
        if self.logs_dir.exists():
            checks['logs_directory'] = {
                'status': 'pass',
                'message': 'Logs directory exists'
            }
        else:
            checks['logs_directory'] = {
                'status': 'fail',
                'message': 'Logs directory not found'
            }
        
        # Check heartbeat monitor
        heartbeat_file = self.project_root / "heartbeat_monitor.py"
        if heartbeat_file.exists():
            checks['heartbeat_monitor'] = {
                'status': 'pass',
                'message': 'Heartbeat monitor available'
            }
        else:
            checks['heartbeat_monitor'] = {
                'status': 'fail',
                'message': 'Heartbeat monitor not found'
            }
        
        # Check if monitoring is running
        try:
            result = subprocess.run(['pgrep', '-f', 'heartbeat_monitor'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                checks['heartbeat_running'] = {
                    'status': 'pass',
                    'message': 'Heartbeat monitor is running'
                }
            else:
                checks['heartbeat_running'] = {
                    'status': 'warn',
                    'message': 'Heartbeat monitor not running'
                }
        except Exception:
            checks['heartbeat_running'] = {
                'status': 'warn',
                'message': 'Could not check heartbeat status'
            }
        
        return checks
    
    def _check_backup_system(self) -> Dict[str, Dict]:
        """Check backup systeem"""
        checks = {}
        
        # Check backup directory
        if self.backup_dir.exists():
            checks['backup_directory'] = {
                'status': 'pass',
                'message': 'Backup directory exists'
            }
        else:
            checks['backup_directory'] = {
                'status': 'fail',
                'message': 'Backup directory not found'
            }
        
        # Check data directory
        data_dir = self.project_root / "data"
        if data_dir.exists():
            checks['data_directory'] = {
                'status': 'pass',
                'message': 'Data directory exists'
            }
        else:
            checks['data_directory'] = {
                'status': 'warn',
                'message': 'Data directory not found'
            }
        
        return checks
    
    def _check_performance_settings(self) -> Dict[str, Dict]:
        """Check performance instellingen"""
        checks = {}
        
        # Check log level
        log_level = os.getenv('LOG_LEVEL', 'INFO')
        if log_level in ['INFO', 'WARNING', 'ERROR']:
            checks['log_level'] = {
                'status': 'pass',
                'message': f'Log level set to {log_level}'
            }
        elif log_level == 'DEBUG':
            checks['log_level'] = {
                'status': 'warn',
                'message': 'DEBUG logging may impact performance'
            }
        else:
            checks['log_level'] = {
                'status': 'fail',
                'message': f'Invalid log level: {log_level}'
            }
        
        # Check trading intervals
        trading_interval = int(os.getenv('TRADING_INTERVAL', '60'))
        if trading_interval >= 30:
            checks['trading_interval'] = {
                'status': 'pass',
                'message': f'Trading interval: {trading_interval}s'
            }
        else:
            checks['trading_interval'] = {
                'status': 'warn',
                'message': f'Trading interval very low: {trading_interval}s'
            }
        
        return checks
    
    def _get_recommendations(self, checks: Dict) -> List[str]:
        """Krijg aanbevelingen voor productie"""
        recommendations = []
        
        for category, category_checks in checks.items():
            for check_name, check_result in category_checks.items():
                if check_result.get('status') == 'fail':
                    recommendations.append(f"🔴 {category.title()}: {check_result['message']}")
                elif check_result.get('status') == 'warn':
                    recommendations.append(f"🟡 {category.title()}: {check_result['message']}")
        
        # Add general recommendations
        if not recommendations:
            recommendations.append("✅ System appears production ready!")
        else:
            recommendations.insert(0, "📋 Address these issues before production deployment:")
        
        return recommendations
    
    def create_backup(self) -> Dict[str, Any]:
        """Maak een backup van kritieke bestanden"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{timestamp}"
            backup_path = self.backup_dir / backup_name
            backup_path.mkdir(exist_ok=True)
            
            # Files to backup
            backup_files = [
                '.env',
                'config/',
                'data/',
                'logs/',
                'strategies/',
                'core/',
                'bot/',
                'analysis/'
            ]
            
            backed_up = []
            for item in backup_files:
                source = self.project_root / item
                if source.exists():
                    if source.is_file():
                        shutil.copy2(source, backup_path / item)
                    else:
                        shutil.copytree(source, backup_path / item, dirs_exist_ok=True)
                    backed_up.append(item)
            
            # Create backup manifest
            manifest = {
                'timestamp': timestamp,
                'backed_up_files': backed_up,
                'backup_size': sum(
                    f.stat().st_size for f in backup_path.rglob('*') if f.is_file()
                )
            }
            
            with open(backup_path / 'manifest.json', 'w') as f:
                json.dump(manifest, f, indent=2)
            
            logger.info(f"✅ Backup created: {backup_name}")
            
            return {
                'success': True,
                'backup_name': backup_name,
                'backup_path': str(backup_path),
                'files_backed_up': len(backed_up),
                'backup_size_mb': round(manifest['backup_size'] / 1024 / 1024, 2)
            }
            
        except Exception as e:
            logger.error(f"❌ Error creating backup: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_deployment_script(self) -> str:
        """Genereer deployment script"""
        script = f"""#!/bin/bash
# Trading Bot Deployment Script
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

echo "🚀 Starting Trading Bot Deployment..."

# Create necessary directories
mkdir -p logs data backups

# Set file permissions
chmod 600 .env
chmod 755 *.py
chmod 755 start_heartbeat.sh

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Run production readiness check
echo "🔍 Running production readiness check..."
python3 -c "
from core.production_manager import ProductionManager
pm = ProductionManager()
result = pm.check_production_readiness()
print(f'Production Ready: {{result[\"production_ready\"]}}')
print(f'Score: {{result[\"overall_score\"]}}%')
"

# Start services
echo "🚀 Starting services..."

# Start heartbeat monitor in background
nohup python3 heartbeat_monitor.py > logs/heartbeat.log 2>&1 &
echo "💓 Heartbeat monitor started"

# Start main bot
echo "🤖 Starting main bot..."
python3 telegram_simple.py

echo "✅ Deployment complete!"
"""
        
        script_path = self.project_root / "deploy.sh"
        with open(script_path, 'w') as f:
            f.write(script)
        
        # Make executable
        os.chmod(script_path, 0o755)
        
        return str(script_path)

# Global production manager
_production_manager = None

def get_production_manager() -> ProductionManager:
    """Get global production manager instance"""
    global _production_manager
    if _production_manager is None:
        _production_manager = ProductionManager()
    return _production_manager
