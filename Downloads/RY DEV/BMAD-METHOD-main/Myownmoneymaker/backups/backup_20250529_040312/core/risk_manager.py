"""
Advanced Risk Management Module with Real-time Balance and Loss Monitoring
"""
import asyncio
import time
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
from dataclasses import dataclass
from loguru import logger

@dataclass
class RiskMetrics:
    """Risk metrics for monitoring"""
    total_balance: Decimal
    available_balance: Decimal
    total_exposure: Decimal
    daily_pnl: Decimal
    unrealized_pnl: Decimal
    max_drawdown: Decimal
    risk_percentage: Decimal
    open_positions: int
    max_daily_loss_reached: bool
    emergency_stop_triggered: bool

@dataclass
class PositionRisk:
    """Position risk information"""
    symbol: str
    size: Decimal
    entry_price: Decimal
    current_price: Decimal
    stop_loss: Optional[Decimal]
    risk_amount: Decimal
    unrealized_pnl: Decimal
    risk_percentage: Decimal

class AdvancedRiskManager:
    """Advanced risk management with real-time monitoring"""

    def __init__(self, exchange_manager):
        self.exchange_manager = exchange_manager

        # Risk parameters
        self.max_risk_per_trade = Decimal('0.02')  # 2% per trade
        self.max_total_risk = Decimal('0.10')  # 10% total portfolio risk
        self.max_daily_loss = Decimal('0.05')  # 5% max daily loss
        self.max_drawdown = Decimal('0.15')  # 15% max drawdown
        self.max_open_positions = 5

        # Tracking variables
        self.daily_start_balance = Decimal('0')
        self.daily_pnl = Decimal('0')
        self.max_balance_today = Decimal('0')
        self.current_drawdown = Decimal('0')
        self.emergency_stop = False
        self.last_balance_check = 0
        self.balance_cache = {}

        # Position tracking
        self.position_risks: Dict[str, PositionRisk] = {}
        self.daily_trades = 0
        self.max_daily_trades = 20

        # Daily tracking will be initialized when first used

    async def _initialize_daily_tracking(self):
        """Initialize daily tracking variables"""
        try:
            current_balance = await self._get_total_balance()
            self.daily_start_balance = current_balance
            self.max_balance_today = current_balance
            logger.info(f"🛡️ Risk Manager initialized - Starting balance: ${current_balance:.2f}")
        except Exception as e:
            logger.error(f"Error initializing daily tracking: {e}")
            self.daily_start_balance = Decimal('1000')  # Fallback

    async def _get_total_balance(self) -> Decimal:
        """Get total balance across all exchanges with caching"""
        current_time = time.time()

        # Use cache if recent (within 10 seconds)
        if current_time - self.last_balance_check < 10 and self.balance_cache:
            return self.balance_cache.get('total', Decimal('0'))

        try:
            total_balance = Decimal('0')
            all_balances = await self.exchange_manager.get_all_balances()

            for exchange_name, balances in all_balances.items():
                for currency, balance in balances.items():
                    if currency == 'USDT':
                        total_balance += Decimal(str(balance.total))
                    elif currency in ['BTC', 'ETH']:
                        # Convert to USDT equivalent (simplified)
                        try:
                            ticker = await self.exchange_manager.get_ticker_from_all(f"{currency}/USDT")
                            if ticker:
                                price = Decimal(str(list(ticker.values())[0].last))
                                total_balance += Decimal(str(balance.total)) * price
                        except:
                            pass

            self.balance_cache = {'total': total_balance}
            self.last_balance_check = current_time
            return total_balance

        except Exception as e:
            logger.error(f"Error getting total balance: {e}")
            return self.balance_cache.get('total', Decimal('1000'))

    async def get_risk_metrics(self) -> RiskMetrics:
        """Get current risk metrics"""
        try:
            total_balance = await self._get_total_balance()

            # Calculate daily P&L
            daily_pnl = total_balance - self.daily_start_balance

            # Update max balance and drawdown
            if total_balance > self.max_balance_today:
                self.max_balance_today = total_balance

            current_drawdown = (self.max_balance_today - total_balance) / self.max_balance_today if self.max_balance_today > 0 else Decimal('0')

            # Calculate total exposure
            total_exposure = sum(pos.risk_amount for pos in self.position_risks.values())

            # Calculate unrealized P&L
            unrealized_pnl = sum(pos.unrealized_pnl for pos in self.position_risks.values())

            # Check emergency conditions
            max_daily_loss_reached = daily_pnl < -(self.daily_start_balance * self.max_daily_loss)
            emergency_stop_triggered = (current_drawdown > self.max_drawdown or
                                      max_daily_loss_reached or
                                      self.emergency_stop)

            return RiskMetrics(
                total_balance=total_balance,
                available_balance=total_balance - total_exposure,
                total_exposure=total_exposure,
                daily_pnl=daily_pnl,
                unrealized_pnl=unrealized_pnl,
                max_drawdown=current_drawdown,
                risk_percentage=(total_exposure / total_balance * 100) if total_balance > 0 else Decimal('0'),
                open_positions=len(self.position_risks),
                max_daily_loss_reached=max_daily_loss_reached,
                emergency_stop_triggered=emergency_stop_triggered
            )

        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return RiskMetrics(
                total_balance=Decimal('0'),
                available_balance=Decimal('0'),
                total_exposure=Decimal('0'),
                daily_pnl=Decimal('0'),
                unrealized_pnl=Decimal('0'),
                max_drawdown=Decimal('0'),
                risk_percentage=Decimal('0'),
                open_positions=0,
                max_daily_loss_reached=False,
                emergency_stop_triggered=True
            )

    async def validate_new_trade(self, symbol: str, side: str, amount: Decimal, price: Decimal, stop_loss: Optional[Decimal] = None) -> Tuple[bool, str]:
        """Validate if a new trade meets risk criteria"""
        try:
            metrics = await self.get_risk_metrics()

            # Check emergency stop
            if metrics.emergency_stop_triggered:
                return False, "🚨 Emergency stop activated - trading halted"

            # Check daily trade limit
            if self.daily_trades >= self.max_daily_trades:
                return False, f"📊 Daily trade limit reached ({self.max_daily_trades})"

            # Check max positions
            if metrics.open_positions >= self.max_open_positions:
                return False, f"📊 Maximum positions reached ({self.max_open_positions})"

            # Calculate trade risk
            if stop_loss:
                risk_per_unit = abs(price - stop_loss)
                trade_risk = amount * risk_per_unit
            else:
                trade_risk = amount * price * self.max_risk_per_trade

            # Check individual trade risk
            max_trade_risk = metrics.total_balance * self.max_risk_per_trade
            if trade_risk > max_trade_risk:
                return False, f"⚠️ Trade risk too high: ${trade_risk:.2f} > ${max_trade_risk:.2f}"

            # Check total portfolio risk
            new_total_risk = metrics.total_exposure + trade_risk
            max_total_risk = metrics.total_balance * self.max_total_risk
            if new_total_risk > max_total_risk:
                return False, f"⚠️ Total portfolio risk too high: {(new_total_risk/metrics.total_balance*100):.1f}% > {self.max_total_risk*100:.1f}%"

            # Check available balance
            trade_value = amount * price
            if trade_value > metrics.available_balance:
                return False, f"💰 Insufficient balance: ${trade_value:.2f} > ${metrics.available_balance:.2f}"

            return True, "✅ Trade approved"

        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return False, f"❌ Validation error: {str(e)}"

    async def add_position(self, symbol: str, side: str, amount: Decimal, entry_price: Decimal, stop_loss: Optional[Decimal] = None):
        """Add a new position to risk tracking"""
        try:
            position_key = f"{symbol}_{side}_{int(time.time())}"

            # Calculate risk amount
            if stop_loss:
                risk_amount = amount * abs(entry_price - stop_loss)
            else:
                risk_amount = amount * entry_price * self.max_risk_per_trade

            # Calculate risk percentage safely
            total_balance = await self._get_total_balance()
            risk_percentage = (risk_amount / total_balance * 100) if total_balance > 0 else Decimal('0')

            position_risk = PositionRisk(
                symbol=symbol,
                size=amount,
                entry_price=entry_price,
                current_price=entry_price,
                stop_loss=stop_loss,
                risk_amount=risk_amount,
                unrealized_pnl=Decimal('0'),
                risk_percentage=risk_percentage
            )

            self.position_risks[position_key] = position_risk
            self.daily_trades += 1

            logger.info(f"📊 Position added: {symbol} - Risk: ${risk_amount:.2f}")

        except Exception as e:
            logger.error(f"Error adding position: {e}")

    async def update_position(self, symbol: str, current_price: Decimal):
        """Update position with current price and calculate P&L"""
        try:
            for key, position in self.position_risks.items():
                if position.symbol == symbol:
                    position.current_price = current_price

                    # Calculate unrealized P&L
                    if 'long' in key or 'buy' in key:
                        position.unrealized_pnl = (current_price - position.entry_price) * position.size
                    else:
                        position.unrealized_pnl = (position.entry_price - current_price) * position.size

        except Exception as e:
            logger.error(f"Error updating position: {e}")

    async def remove_position(self, symbol: str, side: str) -> Decimal:
        """Remove position and return realized P&L"""
        try:
            keys_to_remove = []
            total_pnl = Decimal('0')

            for key, position in self.position_risks.items():
                if position.symbol == symbol and side in key:
                    total_pnl += position.unrealized_pnl
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.position_risks[key]
                logger.info(f"📊 Position removed: {symbol} - P&L: ${total_pnl:.2f}")

            return total_pnl

        except Exception as e:
            logger.error(f"Error removing position: {e}")
            return Decimal('0')

    async def check_emergency_conditions(self) -> bool:
        """Check if emergency stop should be triggered"""
        try:
            metrics = await self.get_risk_metrics()

            if metrics.emergency_stop_triggered and not self.emergency_stop:
                self.emergency_stop = True
                logger.critical("🚨 EMERGENCY STOP TRIGGERED!")
                logger.critical(f"   Daily P&L: ${metrics.daily_pnl:.2f}")
                logger.critical(f"   Drawdown: {metrics.max_drawdown*100:.2f}%")
                logger.critical(f"   Max daily loss reached: {metrics.max_daily_loss_reached}")

            return self.emergency_stop

        except Exception as e:
            logger.error(f"Error checking emergency conditions: {e}")
            return True  # Err on the side of caution

    def reset_emergency_stop(self):
        """Reset emergency stop (manual override)"""
        self.emergency_stop = False
        logger.warning("⚠️ Emergency stop manually reset")

    async def get_risk_summary(self) -> str:
        """Get formatted risk summary"""
        try:
            metrics = await self.get_risk_metrics()

            summary = f"""
🛡️ **Risk Management Dashboard**

💰 **Balance:** ${metrics.total_balance:.2f}
📊 **Daily P&L:** ${metrics.daily_pnl:+.2f}
📈 **Unrealized P&L:** ${metrics.unrealized_pnl:+.2f}

⚠️ **Risk Metrics:**
• Portfolio Risk: {metrics.risk_percentage:.1f}% / {self.max_total_risk*100:.1f}%
• Open Positions: {metrics.open_positions} / {self.max_open_positions}
• Daily Trades: {self.daily_trades} / {self.max_daily_trades}
• Max Drawdown: {metrics.max_drawdown*100:.2f}% / {self.max_drawdown*100:.1f}%

🚨 **Status:** {'🔴 EMERGENCY STOP' if metrics.emergency_stop_triggered else '🟢 ACTIVE'}
            """

            return summary.strip()

        except Exception as e:
            logger.error(f"Error generating risk summary: {e}")
            return "❌ Error generating risk summary"
