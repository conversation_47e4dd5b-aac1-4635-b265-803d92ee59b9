"""
Beginner Education System
Educational content and quiz system for new traders
"""

import sqlite3
from datetime import datetime
from typing import Dict


class BeginnerEducationManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()

        # Education content
        self.education_modules = {
            "intro": {
                "title_nl": "Introductie tot Crypto Trading",
                "title_en": "Introduction to Crypto Trading",
                "content_nl": """
📚 **Welkom bij Crypto Trading!**

🎯 **Wat ga je leren:**
• Basis van cryptocurrency
• Hoe trading werkt
• Risico's en veiligheid
• Praktische trading tips

💡 **Belangrijke Concepten:**
• **Buy (Kopen)**: Cryptocurrency kopen met Euro's
• **Sell (Verkopen)**: Cryptocurrency verkopen voor Euro's
• **Stop Loss**: Automatisch verkopen bij verlies
• **Take Profit**: Automatisch verkopen bij winst

⚠️ **Risico Waarschuwing:**
• Je kunt geld verliezen
• Investeer alleen wat je kunt missen
• Start met kleine bedragen
• Leer voordat je grote trades doet

🔰 **Beginner Voordelen:**
• Maximaal €25 per trade
• Maximaal €100 per dag
• Alleen veilige crypto pairs
• Extra begeleiding en tips
""",
                "content_en": """
📚 **Welcome to Crypto Trading!**

🎯 **What you'll learn:**
• Cryptocurrency basics
• How trading works
• Risks and safety
• Practical trading tips

💡 **Important Concepts:**
• **Buy**: Purchase cryptocurrency with Euros
• **Sell**: Sell cryptocurrency for Euros
• **Stop Loss**: Automatically sell at loss
• **Take Profit**: Automatically sell at profit

⚠️ **Risk Warning:**
• You can lose money
• Only invest what you can afford to lose
• Start with small amounts
• Learn before making big trades

🔰 **Beginner Benefits:**
• Maximum €25 per trade
• Maximum €100 per day
• Only safe crypto pairs
• Extra guidance and tips
""",
            },
            "risk_management": {
                "title_nl": "Risicobeheer",
                "title_en": "Risk Management",
                "content_nl": """
🛡️ **Risicobeheer is Essentieel!**

📊 **De 3 Gouden Regels:**
1. **Nooit meer dan 5% van je geld in één trade**
2. **Altijd een stop loss instellen**
3. **Emoties buiten de trading houden**

💰 **Geld Management:**
• Start met €50-100 totaal
• Maximaal €25 per trade (beginner)
• Spreeid over verschillende crypto's
• Houd altijd reserve geld

📉 **Stop Loss Uitleg:**
• Stel in bij -5% tot -10%
• Voorkomt grote verliezen
• Automatisch verkopen
• Emoties eruit houden

📈 **Take Profit Uitleg:**
• Stel in bij +10% tot +20%
• Winst pakken op tijd
• Niet te hebberig worden
• Consistent kleine winsten

🧠 **Psychologie:**
• FOMO (Fear of Missing Out) vermijden
• Niet paniek verkopen
• Plan maken en volgen
• Geduld hebben
""",
                "content_en": """
🛡️ **Risk Management is Essential!**

📊 **The 3 Golden Rules:**
1. **Never more than 5% of your money in one trade**
2. **Always set a stop loss**
3. **Keep emotions out of trading**

💰 **Money Management:**
• Start with €50-100 total
• Maximum €25 per trade (beginner)
• Spread across different cryptos
• Always keep reserve money

📉 **Stop Loss Explained:**
• Set at -5% to -10%
• Prevents big losses
• Automatic selling
• Remove emotions

📈 **Take Profit Explained:**
• Set at +10% to +20%
• Take profits on time
• Don't be too greedy
• Consistent small wins

🧠 **Psychology:**
• Avoid FOMO (Fear of Missing Out)
• Don't panic sell
• Make plan and follow it
• Be patient
""",
            },
        }

        # Quiz questions
        self.quiz_questions = [
            {
                "question_nl": "Wat is de maximale trade grootte voor beginners?",
                "question_en": "What is the maximum trade size for beginners?",
                "options_nl": ["€10", "€25", "€50", "€100"],
                "options_en": ["€10", "€25", "€50", "€100"],
                "correct": 1,  # €25
                "explanation_nl": "Beginners kunnen maximaal €25 per trade investeren voor veiligheid.",
                "explanation_en": "Beginners can invest maximum €25 per trade for safety.",
            },
            {
                "question_nl": "Wat doet een stop loss?",
                "question_en": "What does a stop loss do?",
                "options_nl": ["Koopt automatisch", "Verkoopt bij verlies", "Verhoogt winst", "Niets"],
                "options_en": ["Buys automatically", "Sells at loss", "Increases profit", "Nothing"],
                "correct": 1,  # Verkoopt bij verlies
                "explanation_nl": "Een stop loss verkoopt automatisch je crypto als de prijs te veel daalt.",
                "explanation_en": "A stop loss automatically sells your crypto if the price drops too much.",
            },
            {
                "question_nl": "Hoeveel van je totale geld mag je in één trade stoppen?",
                "question_en": "How much of your total money should you put in one trade?",
                "options_nl": ["50%", "25%", "10%", "5%"],
                "options_en": ["50%", "25%", "10%", "5%"],
                "correct": 3,  # 5%
                "explanation_nl": "Nooit meer dan 5% van je totale geld in één trade voor veiligheid.",
                "explanation_en": "Never more than 5% of your total money in one trade for safety.",
            },
            {
                "question_nl": "Wat is FOMO in trading?",
                "question_en": "What is FOMO in trading?",
                "options_nl": ["Fear of Missing Out", "Fear of Money Out", "Fast Order Market Out", "Weet ik niet"],
                "options_en": ["Fear of Missing Out", "Fear of Money Out", "Fast Order Market Out", "I don't know"],
                "correct": 0,  # Fear of Missing Out
                "explanation_nl": "FOMO betekent Fear of Missing Out - angst om kansen te missen.",
                "explanation_en": "FOMO means Fear of Missing Out - fear of missing opportunities.",
            },
            {
                "question_nl": "Wanneer moet je een take profit instellen?",
                "question_en": "When should you set a take profit?",
                "options_nl": ["Nooit", "Bij +5%", "Bij +10-20%", "Bij +50%"],
                "options_en": ["Never", "At +5%", "At +10-20%", "At +50%"],
                "correct": 2,  # Bij +10-20%
                "explanation_nl": "Take profit bij +10-20% zorgt voor consistente kleine winsten.",
                "explanation_en": "Take profit at +10-20% ensures consistent small profits.",
            },
            {
                "question_nl": "Wat is het belangrijkste bij crypto trading?",
                "question_en": "What is most important in crypto trading?",
                "options_nl": ["Snel rijk worden", "Risicobeheer", "Veel trades doen", "Grote winsten"],
                "options_en": ["Getting rich quick", "Risk management", "Many trades", "Big profits"],
                "correct": 1,  # Risicobeheer
                "explanation_nl": "Risicobeheer is het belangrijkste om je geld te beschermen.",
                "explanation_en": "Risk management is most important to protect your money.",
            },
            {
                "question_nl": "Hoeveel mag je maximaal per dag traden als beginner?",
                "question_en": "What is the maximum daily trading amount for beginners?",
                "options_nl": ["€50", "€75", "€100", "€200"],
                "options_en": ["€50", "€75", "€100", "€200"],
                "correct": 2,  # €100
                "explanation_nl": "Beginners hebben een dagelijkse limiet van €100 voor veiligheid.",
                "explanation_en": "Beginners have a daily limit of €100 for safety.",
            },
            {
                "question_nl": "Wat moet je doen als je emotioneel wordt tijdens trading?",
                "question_en": "What should you do if you get emotional during trading?",
                "options_nl": ["Meer traden", "Stoppen en pauze nemen", "Groter bedrag inzetten", "Sneller handelen"],
                "options_en": ["Trade more", "Stop and take a break", "Invest bigger amount", "Trade faster"],
                "correct": 1,  # Stoppen en pauze nemen
                "explanation_nl": "Bij emoties moet je stoppen en een pauze nemen om fouten te voorkomen.",
                "explanation_en": "When emotional, stop and take a break to prevent mistakes.",
            },
            {
                "question_nl": "Welke crypto pairs zijn toegestaan voor beginners?",
                "question_en": "Which crypto pairs are allowed for beginners?",
                "options_nl": ["Alle pairs", "Alleen BTC/ETH/BNB/ADA", "Alleen nieuwe coins", "Alleen meme coins"],
                "options_en": ["All pairs", "Only BTC/ETH/BNB/ADA", "Only new coins", "Only meme coins"],
                "correct": 1,  # Alleen BTC/ETH/BNB/ADA
                "explanation_nl": "Beginners kunnen alleen handelen in veilige, grote crypto pairs.",
                "explanation_en": "Beginners can only trade in safe, major crypto pairs.",
            },
            {
                "question_nl": "Wat is de beste strategie voor beginners?",
                "question_en": "What is the best strategy for beginners?",
                "options_nl": ["Day trading", "Kleine bedragen, leren", "All-in gaan", "Lenen voor trading"],
                "options_en": ["Day trading", "Small amounts, learning", "Going all-in", "Borrowing for trading"],
                "correct": 1,  # Kleine bedragen, leren
                "explanation_nl": "Beginners moeten starten met kleine bedragen en veel leren.",
                "explanation_en": "Beginners should start with small amounts and learn a lot.",
            },
        ]

    def init_database(self):
        """Initialize education database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Education progress table (already exists in beginner_trading_system.py)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS beginner_education (
                user_id INTEGER PRIMARY KEY,
                completed_intro BOOLEAN DEFAULT 0,
                completed_risk_warning BOOLEAN DEFAULT 0,
                completed_first_trade BOOLEAN DEFAULT 0,
                quiz_score INTEGER DEFAULT 0,
                quiz_attempts INTEGER DEFAULT 0,
                can_trade_real_money BOOLEAN DEFAULT 0,
                education_level TEXT DEFAULT 'novice',
                last_quiz_date TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        """)

        # Quiz answers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS quiz_answers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                question_number INTEGER,
                selected_answer INTEGER,
                is_correct BOOLEAN,
                attempt_number INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        """)

        conn.commit()
        conn.close()

    def get_education_progress(self, user_id: int) -> Dict:
        """Get user's education progress"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT * FROM beginner_education WHERE user_id = ?
        """,
            (user_id,),
        )

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                "user_id": result[0],
                "completed_intro": bool(result[1]),
                "completed_risk_warning": bool(result[2]),
                "completed_first_trade": bool(result[3]),
                "quiz_score": result[4],
                "quiz_attempts": result[5],
                "can_trade_real_money": bool(result[6]),
                "education_level": result[7] if len(result) > 7 else "novice",
                "last_quiz_date": result[8] if len(result) > 8 else None,
            }
        else:
            # Create new record
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(
                """
                INSERT INTO beginner_education (user_id) VALUES (?)
            """,
                (user_id,),
            )
            conn.commit()
            conn.close()

            return {
                "user_id": user_id,
                "completed_intro": False,
                "completed_risk_warning": False,
                "completed_first_trade": False,
                "quiz_score": 0,
                "quiz_attempts": 0,
                "can_trade_real_money": False,
                "education_level": "novice",
                "last_quiz_date": None,
            }

    def complete_intro(self, user_id: int) -> Dict:
        """Mark intro as completed"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            UPDATE beginner_education
            SET completed_intro = 1
            WHERE user_id = ?
        """,
            (user_id,),
        )

        if cursor.rowcount == 0:
            cursor.execute(
                """
                INSERT INTO beginner_education (user_id, completed_intro)
                VALUES (?, 1)
            """,
                (user_id,),
            )

        conn.commit()
        conn.close()

        return {"success": True, "message": "Introductie voltooid!"}

    def complete_risk_warning(self, user_id: int) -> Dict:
        """Mark risk warning as completed"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            UPDATE beginner_education
            SET completed_risk_warning = 1
            WHERE user_id = ?
        """,
            (user_id,),
        )

        conn.commit()
        conn.close()

        return {"success": True, "message": "Risico waarschuwing geaccepteerd!"}

    def start_quiz(self, user_id: int) -> Dict:
        """Start a new quiz attempt"""
        progress = self.get_education_progress(user_id)

        if not progress["completed_intro"]:
            return {"success": False, "error": "Voltooi eerst de introductie"}

        if not progress["completed_risk_warning"]:
            return {"success": False, "error": "Accepteer eerst de risico waarschuwing"}

        # Limit quiz attempts
        if progress["quiz_attempts"] >= 3:
            return {"success": False, "error": "Maximum 3 quiz pogingen bereikt. Neem contact op met support."}

        return {
            "success": True,
            "message": "Quiz gestart!",
            "total_questions": len(self.quiz_questions),
            "passing_score": 7,
        }

    def submit_quiz_answer(self, user_id: int, question_number: int, selected_answer: int) -> Dict:
        """Submit answer for quiz question"""
        if question_number >= len(self.quiz_questions):
            return {"success": False, "error": "Ongeldige vraag nummer"}

        question = self.quiz_questions[question_number]
        is_correct = selected_answer == question["correct"]

        # Get current attempt number
        progress = self.get_education_progress(user_id)
        attempt_number = progress["quiz_attempts"] + 1

        # Record answer
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT INTO quiz_answers
            (user_id, question_number, selected_answer, is_correct, attempt_number)
            VALUES (?, ?, ?, ?, ?)
        """,
            (user_id, question_number, selected_answer, is_correct, attempt_number),
        )

        conn.commit()
        conn.close()

        return {
            "success": True,
            "is_correct": is_correct,
            "explanation": question["explanation_nl"],
            "correct_answer": question["options_nl"][question["correct"]],
        }

    def complete_quiz(self, user_id: int) -> Dict:
        """Complete quiz and calculate score"""
        progress = self.get_education_progress(user_id)
        attempt_number = progress["quiz_attempts"] + 1

        # Calculate score for this attempt
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT COUNT(*) as total, SUM(CASE WHEN is_correct THEN 1 ELSE 0 END) as correct
            FROM quiz_answers
            WHERE user_id = ? AND attempt_number = ?
        """,
            (user_id, attempt_number),
        )

        result = cursor.fetchone()
        total_questions = result[0]
        correct_answers = result[1]

        score = correct_answers if total_questions == len(self.quiz_questions) else 0
        passed = score >= 7

        # Update education progress
        cursor.execute(
            """
            UPDATE beginner_education
            SET quiz_score = ?, quiz_attempts = ?, last_quiz_date = ?,
                can_trade_real_money = ?
            WHERE user_id = ?
        """,
            (score, attempt_number, datetime.now(), passed, user_id),
        )

        conn.commit()
        conn.close()

        return {
            "success": True,
            "score": score,
            "total": len(self.quiz_questions),
            "passed": passed,
            "can_trade": passed,
            "message": f"Quiz voltooid! Score: {score}/{len(self.quiz_questions)}",
        }

    def get_education_status_text(self, user_id: int, lang: str = "nl") -> str:
        """Get education status text"""
        progress = self.get_education_progress(user_id)

        if lang == "nl":
            text = f"""
🎓 **Beginner Educatie Status**

📚 **Voortgang:**
{"✅" if progress["completed_intro"] else "❌"} Introductie voltooid
{"✅" if progress["completed_risk_warning"] else "❌"} Risico waarschuwing geaccepteerd
{"✅" if progress["quiz_score"] >= 7 else "❌"} Quiz geslaagd ({progress["quiz_score"]}/10)
{"✅" if progress["can_trade_real_money"] else "❌"} Trading geactiveerd

📊 **Quiz Statistieken:**
• Beste score: {progress["quiz_score"]}/10
• Pogingen gebruikt: {progress["quiz_attempts"]}/3
• Status: {"✅ Geslaagd" if progress["quiz_score"] >= 7 else "❌ Niet geslaagd"}

🎯 **Volgende Stappen:**
"""

            if not progress["completed_intro"]:
                text += "• Voltooi introductie met /beginner_intro\n"
            elif not progress["completed_risk_warning"]:
                text += "• Accepteer risico waarschuwing\n"
            elif progress["quiz_score"] < 7:
                text += f"• Haal quiz (nog {3 - progress['quiz_attempts']} pogingen)\n"
            elif not progress["can_trade_real_money"]:
                text += "• Wacht op activatie door support\n"
            else:
                text += "• Je kunt nu beginnen met real trading! 🎉\n"

        else:
            text = f"""
🎓 **Beginner Education Status**

📚 **Progress:**
{"✅" if progress["completed_intro"] else "❌"} Introduction completed
{"✅" if progress["completed_risk_warning"] else "❌"} Risk warning accepted
{"✅" if progress["quiz_score"] >= 7 else "❌"} Quiz passed ({progress["quiz_score"]}/10)
{"✅" if progress["can_trade_real_money"] else "❌"} Trading activated

📊 **Quiz Statistics:**
• Best score: {progress["quiz_score"]}/10
• Attempts used: {progress["quiz_attempts"]}/3
• Status: {"✅ Passed" if progress["quiz_score"] >= 7 else "❌ Not passed"}

🎯 **Next Steps:**
"""

            if not progress["completed_intro"]:
                text += "• Complete introduction with /beginner_intro\n"
            elif not progress["completed_risk_warning"]:
                text += "• Accept risk warning\n"
            elif progress["quiz_score"] < 7:
                text += f"• Pass quiz ({3 - progress['quiz_attempts']} attempts left)\n"
            elif not progress["can_trade_real_money"]:
                text += "• Wait for activation by support\n"
            else:
                text += "• You can now start real trading! 🎉\n"

        return text
