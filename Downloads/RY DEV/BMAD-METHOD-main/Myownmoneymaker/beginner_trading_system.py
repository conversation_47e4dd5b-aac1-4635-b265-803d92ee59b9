"""
Beginner Trading System for Real Money Trading
Simplified interface with basic features and extra safety measures
"""

import sqlite3
import json
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from decimal import Decimal

class BeginnerTradingManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
        
        # Beginner trading limits (much more conservative)
        self.beginner_max_trade_eur = 25      # Maximum €25 per trade
        self.beginner_daily_limit = 100       # Maximum €100 per day
        self.beginner_max_positions = 2       # Maximum 2 open positions
        
        # Allowed trading pairs for beginners (major pairs only)
        self.beginner_allowed_pairs = [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT'
        ]
        
        # Simple strategies for beginners
        self.beginner_strategies = {
            'manual': 'Handmatige trading - jij beslist alles',
            'dca': 'Dollar Cost Averaging - regelmatig kleine bedragen kopen'
        }
        
    def init_database(self):
        """Initialize beginner trading tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Beginner trading accounts
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS beginner_trading_accounts (
                user_id INTEGER PRIMARY KEY,
                is_enabled BOOLEAN DEFAULT 0,
                experience_level TEXT DEFAULT 'beginner',
                total_invested REAL DEFAULT 0.0,
                current_balance REAL DEFAULT 0.0,
                total_trades INTEGER DEFAULT 0,
                successful_trades INTEGER DEFAULT 0,
                max_daily_amount REAL DEFAULT 100.0,
                max_trade_amount REAL DEFAULT 25.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Simple trade orders for beginners
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS beginner_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                symbol TEXT,
                side TEXT, -- 'buy' or 'sell'
                amount_eur REAL,
                crypto_amount REAL,
                price REAL,
                status TEXT DEFAULT 'pending', -- 'pending', 'filled', 'cancelled'
                stop_loss_price REAL,
                take_profit_price REAL,
                is_trailing_stop BOOLEAN DEFAULT 0,
                trailing_stop_percentage REAL,
                order_type TEXT DEFAULT 'manual', -- 'manual', 'dca'
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                filled_at TIMESTAMP,
                profit_loss REAL DEFAULT 0.0,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Beginner education progress
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS beginner_education (
                user_id INTEGER PRIMARY KEY,
                completed_intro BOOLEAN DEFAULT 0,
                completed_risk_warning BOOLEAN DEFAULT 0,
                completed_first_trade BOOLEAN DEFAULT 0,
                quiz_score INTEGER DEFAULT 0,
                can_trade_real_money BOOLEAN DEFAULT 0,
                education_level TEXT DEFAULT 'novice',
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def is_beginner_eligible(self, user_id: int) -> Tuple[bool, str]:
        """Check if user is eligible for beginner trading"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if user completed education
        cursor.execute('''
            SELECT completed_intro, completed_risk_warning, quiz_score, can_trade_real_money
            FROM beginner_education WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            return False, "Educatie niet voltooid. Start met /beginner_intro"
        
        completed_intro, completed_risk, quiz_score, can_trade = result
        
        if not completed_intro:
            return False, "Voltooi eerst de introductie met /beginner_intro"
        
        if not completed_risk:
            return False, "Voltooi eerst de risico waarschuwing"
        
        if quiz_score < 7:  # Minimum 7/10 correct
            return False, f"Quiz score te laag ({quiz_score}/10). Minimaal 7 vereist."
        
        if not can_trade:
            return False, "Trading nog niet geactiveerd. Neem contact op met support."
        
        return True, "Geautoriseerd voor beginner trading"
    
    def enable_beginner_trading(self, user_id: int) -> Dict:
        """Enable beginner trading for user after education"""
        eligible, reason = self.is_beginner_eligible(user_id)
        
        if not eligible:
            return {"success": False, "error": reason}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO beginner_trading_accounts 
            (user_id, is_enabled, experience_level)
            VALUES (?, ?, ?)
        ''', (user_id, True, 'beginner'))
        
        conn.commit()
        conn.close()
        
        return {"success": True, "message": "Beginner trading geactiveerd!"}
    
    def get_beginner_account(self, user_id: int) -> Optional[Dict]:
        """Get beginner trading account info"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM beginner_trading_accounts WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'user_id': result[0],
                'is_enabled': bool(result[1]),
                'experience_level': result[2],
                'total_invested': result[3],
                'current_balance': result[4],
                'total_trades': result[5],
                'successful_trades': result[6],
                'max_daily_amount': result[7],
                'max_trade_amount': result[8],
                'created_at': result[9]
            }
        return None
    
    def can_place_trade(self, user_id: int, amount_eur: float) -> Tuple[bool, str]:
        """Check if beginner can place this trade"""
        account = self.get_beginner_account(user_id)
        
        if not account or not account['is_enabled']:
            return False, "Beginner trading niet geactiveerd"
        
        # Check trade amount limit
        if amount_eur > account['max_trade_amount']:
            return False, f"Bedrag te hoog. Maximum: €{account['max_trade_amount']:.0f}"
        
        # Check daily limit
        today = datetime.now().date()
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT SUM(amount_eur) FROM beginner_trades 
            WHERE user_id = ? AND DATE(created_at) = ? AND status != 'cancelled'
        ''', (user_id, today))
        
        daily_total = cursor.fetchone()[0] or 0
        conn.close()
        
        if daily_total + amount_eur > account['max_daily_amount']:
            remaining = account['max_daily_amount'] - daily_total
            return False, f"Dagelijkse limiet overschreden. Nog beschikbaar: €{remaining:.2f}"
        
        # Check maximum positions
        open_positions = self.get_open_positions_count(user_id)
        if open_positions >= self.beginner_max_positions:
            return False, f"Maximum {self.beginner_max_positions} posities tegelijk toegestaan"
        
        return True, "Trade toegestaan"
    
    def get_open_positions_count(self, user_id: int) -> int:
        """Get number of open positions"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) FROM beginner_trades 
            WHERE user_id = ? AND status = 'filled' AND side = 'buy'
            AND id NOT IN (
                SELECT bt1.id FROM beginner_trades bt1 
                JOIN beginner_trades bt2 ON bt1.user_id = bt2.user_id 
                AND bt1.symbol = bt2.symbol 
                WHERE bt1.side = 'buy' AND bt2.side = 'sell' 
                AND bt2.created_at > bt1.created_at
            )
        ''', (user_id,))
        
        result = cursor.fetchone()[0]
        conn.close()
        return result
    
    def place_beginner_trade(self, user_id: int, symbol: str, side: str, amount_eur: float, 
                           stop_loss_percentage: float = None, take_profit_percentage: float = None,
                           trailing_stop: bool = False) -> Dict:
        """Place a beginner trade with safety checks"""
        
        # Validate symbol
        if symbol not in self.beginner_allowed_pairs:
            return {"success": False, "error": f"Alleen deze pairs toegestaan: {', '.join(self.beginner_allowed_pairs)}"}
        
        # Check if trade is allowed
        can_trade, reason = self.can_place_trade(user_id, amount_eur)
        if not can_trade:
            return {"success": False, "error": reason}
        
        # Simulate getting current price (in real implementation, use exchange API)
        current_price = self.get_current_price_simulation(symbol)
        crypto_amount = amount_eur / current_price
        
        # Calculate stop loss and take profit prices
        stop_loss_price = None
        take_profit_price = None
        
        if side == 'buy':
            if stop_loss_percentage:
                stop_loss_price = current_price * (1 - stop_loss_percentage / 100)
            if take_profit_percentage:
                take_profit_price = current_price * (1 + take_profit_percentage / 100)
        
        # Record the trade
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO beginner_trades 
            (user_id, symbol, side, amount_eur, crypto_amount, price, stop_loss_price, 
             take_profit_price, is_trailing_stop, trailing_stop_percentage, status, filled_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, symbol, side, amount_eur, crypto_amount, current_price,
              stop_loss_price, take_profit_price, trailing_stop, 
              stop_loss_percentage if trailing_stop else None, 'filled', datetime.now()))
        
        trade_id = cursor.lastrowid
        
        # Update account statistics
        cursor.execute('''
            UPDATE beginner_trading_accounts 
            SET total_trades = total_trades + 1,
                total_invested = total_invested + ?
            WHERE user_id = ?
        ''', (amount_eur if side == 'buy' else 0, user_id))
        
        conn.commit()
        conn.close()
        
        return {
            "success": True,
            "trade_id": trade_id,
            "symbol": symbol,
            "side": side,
            "amount_eur": amount_eur,
            "crypto_amount": crypto_amount,
            "price": current_price,
            "stop_loss_price": stop_loss_price,
            "take_profit_price": take_profit_price,
            "message": f"✅ {side.upper()} order geplaatst voor €{amount_eur:.2f}"
        }
    
    def get_current_price_simulation(self, symbol: str) -> float:
        """Simulate current price (replace with real exchange API)"""
        prices = {
            'BTC/USDT': 95234.50,
            'ETH/USDT': 3456.78,
            'BNB/USDT': 645.32,
            'ADA/USDT': 1.23
        }
        return prices.get(symbol, 100.0)
    
    def get_beginner_portfolio(self, user_id: int) -> Dict:
        """Get beginner's portfolio overview"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get all filled buy orders that haven't been sold
        cursor.execute('''
            SELECT symbol, SUM(crypto_amount), AVG(price), SUM(amount_eur)
            FROM beginner_trades 
            WHERE user_id = ? AND status = 'filled' AND side = 'buy'
            AND id NOT IN (
                SELECT bt1.id FROM beginner_trades bt1 
                JOIN beginner_trades bt2 ON bt1.user_id = bt2.user_id 
                AND bt1.symbol = bt2.symbol 
                WHERE bt1.side = 'buy' AND bt2.side = 'sell' 
                AND bt2.created_at > bt1.created_at
            )
            GROUP BY symbol
        ''', (user_id,))
        
        holdings = cursor.fetchall()
        
        # Get account info
        cursor.execute('''
            SELECT total_invested, total_trades, successful_trades 
            FROM beginner_trading_accounts WHERE user_id = ?
        ''', (user_id,))
        
        account_info = cursor.fetchone()
        conn.close()
        
        portfolio = []
        total_current_value = 0
        
        for symbol, amount, avg_price, invested in holdings:
            current_price = self.get_current_price_simulation(symbol)
            current_value = amount * current_price
            profit_loss = current_value - invested
            profit_percentage = (profit_loss / invested) * 100 if invested > 0 else 0
            
            portfolio.append({
                'symbol': symbol,
                'amount': amount,
                'avg_price': avg_price,
                'current_price': current_price,
                'invested': invested,
                'current_value': current_value,
                'profit_loss': profit_loss,
                'profit_percentage': profit_percentage
            })
            
            total_current_value += current_value
        
        if account_info:
            total_invested, total_trades, successful_trades = account_info
            success_rate = (successful_trades / total_trades * 100) if total_trades > 0 else 0
        else:
            total_invested = total_trades = successful_trades = success_rate = 0
        
        return {
            'holdings': portfolio,
            'total_invested': total_invested,
            'total_current_value': total_current_value,
            'total_profit_loss': total_current_value - total_invested,
            'total_trades': total_trades,
            'successful_trades': successful_trades,
            'success_rate': success_rate
        }
    
    def get_beginner_trading_interface_text(self, user_id: int, lang: str = 'nl') -> str:
        """Get beginner trading interface text"""
        eligible, reason = self.is_beginner_eligible(user_id)
        
        if not eligible:
            if lang == 'nl':
                return f"""
🔰 **Beginner Trading**

❌ **Niet Beschikbaar**
{reason}

📚 **Vereisten:**
• Voltooi introductie cursus
• Accepteer risico waarschuwing  
• Haal quiz (minimaal 7/10)
• Activatie door support

🎓 **Start hier:** /beginner_intro
"""
            else:
                return f"""
🔰 **Beginner Trading**

❌ **Not Available**
{reason}

📚 **Requirements:**
• Complete intro course
• Accept risk warning
• Pass quiz (minimum 7/10)
• Activation by support

🎓 **Start here:** /beginner_intro
"""
        
        account = self.get_beginner_account(user_id)
        portfolio = self.get_beginner_portfolio(user_id)
        
        if lang == 'nl':
            text = f"""
🔰 **Beginner Real Trading**

💰 **Account Status:**
• Status: {'✅ Actief' if account and account['is_enabled'] else '❌ Inactief'}
• Totaal geïnvesteerd: €{portfolio['total_invested']:.2f}
• Huidige waarde: €{portfolio['total_current_value']:.2f}
• Winst/Verlies: €{portfolio['total_profit_loss']:.2f}

📊 **Trading Statistieken:**
• Totale trades: {portfolio['total_trades']}
• Succesvolle trades: {portfolio['successful_trades']}
• Success rate: {portfolio['success_rate']:.1f}%

🛡️ **Veiligheidslimieten:**
• Max per trade: €{account['max_trade_amount'] if account else 25:.0f}
• Max per dag: €{account['max_daily_amount'] if account else 100:.0f}
• Max posities: {self.beginner_max_positions}

📈 **Toegestane Pairs:**
{', '.join(self.beginner_allowed_pairs)}

⚠️ **Beginner Mode:** Extra veiligheidsmaatregelen actief
"""
        else:
            text = f"""
🔰 **Beginner Real Trading**

💰 **Account Status:**
• Status: {'✅ Active' if account and account['is_enabled'] else '❌ Inactive'}
• Total invested: €{portfolio['total_invested']:.2f}
• Current value: €{portfolio['total_current_value']:.2f}
• Profit/Loss: €{portfolio['total_profit_loss']:.2f}

📊 **Trading Statistics:**
• Total trades: {portfolio['total_trades']}
• Successful trades: {portfolio['successful_trades']}
• Success rate: {portfolio['success_rate']:.1f}%

🛡️ **Safety Limits:**
• Max per trade: €{account['max_trade_amount'] if account else 25:.0f}
• Max per day: €{account['max_daily_amount'] if account else 100:.0f}
• Max positions: {self.beginner_max_positions}

📈 **Allowed Pairs:**
{', '.join(self.beginner_allowed_pairs)}

⚠️ **Beginner Mode:** Extra safety measures active
"""
        
        return text
