"""
config package initializer

Do<PERSON>:
- <PERSON><PERSON> de <PERSON>-configuratie globaal beschik<PERSON>ar als 'settings'
- <PERSON><PERSON><PERSON> voor veilige, consistente toegang tot instellingen via: from config import settings
"""

from .settings import Settings

try:
    settings = Settings()
except Exception as e:
    # Zorg voor fallback logging bij importproblemen
    import sys
    print(f"[CONFIG INIT ERROR] Kon settings niet initialiseren: {e}", file=sys.stderr)
    settings = None
