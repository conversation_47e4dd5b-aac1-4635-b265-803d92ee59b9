#!/usr/bin/env python3
"""
Advanced Configuration Manager voor Trading Bot
Centralized configuration management met validatie, encryptie en environment support
"""

import os
import json
import yaml
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pathlib import Path
from cryptography.fernet import Fernet
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


@dataclass
class ConfigSection:
    """Represents a configuration section with validation"""
    name: str
    required_fields: List[str] = field(default_factory=list)
    optional_fields: List[str] = field(default_factory=list)
    encrypted_fields: List[str] = field(default_factory=list)
    validation_rules: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """
    Advanced Configuration Manager
    
    Features:
    - Environment-based configuration
    - Encryption voor gevoelige data
    - Validation en type checking
    - Hot-reload ondersteuning
    - Configuration templates
    - Backup en versioning
    """
    
    def __init__(self, config_dir: str = "config", environment: str = "development"):
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.config_cache = {}
        self.encryption_key = None
        self.watchers = {}
        
        # Ensure config directory exists
        self.config_dir.mkdir(exist_ok=True)
        
        # Load environment variables
        load_dotenv()
        
        # Initialize encryption if key is available
        self._init_encryption()
        
        # Define configuration sections
        self._define_config_sections()
        
        # Load all configurations
        self._load_all_configs()
    
    def _init_encryption(self):
        """Initialize encryption for sensitive data"""
        encryption_key = os.getenv("ENCRYPTION_KEY")
        if encryption_key:
            try:
                self.encryption_key = Fernet(encryption_key.encode())
                logger.info("🔐 Encryption initialized successfully")
            except Exception as e:
                logger.warning(f"⚠️ Failed to initialize encryption: {e}")
        else:
            logger.warning("⚠️ No encryption key found - sensitive data will not be encrypted")
    
    def _define_config_sections(self):
        """Define all configuration sections with validation rules"""
        self.config_sections = {
            "telegram": ConfigSection(
                name="telegram",
                required_fields=["bot_token", "admin_user_id"],
                encrypted_fields=["bot_token"],
                validation_rules={
                    "bot_token": {"type": str, "min_length": 40},
                    "admin_user_id": {"type": int, "min_value": 1}
                }
            ),
            "exchanges": ConfigSection(
                name="exchanges",
                required_fields=["default_exchange"],
                optional_fields=["kucoin", "mexc", "binance"],
                encrypted_fields=["kucoin.api_key", "kucoin.secret_key", "mexc.api_key", "mexc.secret_key"],
                validation_rules={
                    "default_exchange": {"type": str, "choices": ["kucoin", "mexc", "binance"]}
                }
            ),
            "trading": ConfigSection(
                name="trading",
                required_fields=["enabled", "mode"],
                optional_fields=["daily_limit", "max_trades_per_day", "risk_per_trade"],
                validation_rules={
                    "mode": {"type": str, "choices": ["test", "paper", "live"]},
                    "daily_limit": {"type": float, "min_value": 0},
                    "risk_per_trade": {"type": float, "min_value": 0, "max_value": 1}
                }
            ),
            "security": ConfigSection(
                name="security",
                required_fields=["rate_limit_enabled"],
                optional_fields=["max_login_attempts", "session_timeout", "enable_2fa"],
                validation_rules={
                    "max_login_attempts": {"type": int, "min_value": 1, "max_value": 10},
                    "session_timeout": {"type": int, "min_value": 300}
                }
            ),
            "database": ConfigSection(
                name="database",
                required_fields=["url"],
                optional_fields=["backup_enabled", "backup_interval"],
                validation_rules={
                    "backup_interval": {"type": int, "min_value": 1}
                }
            )
        }
    
    def _load_all_configs(self):
        """Load all configuration files"""
        try:
            # Load main configuration
            self._load_main_config()
            
            # Load environment-specific config
            self._load_environment_config()
            
            # Load user-specific configs
            self._load_user_configs()
            
            # Validate all configurations
            self._validate_all_configs()
            
            logger.info(f"✅ All configurations loaded for environment: {self.environment}")
            
        except Exception as e:
            logger.error(f"❌ Failed to load configurations: {e}")
            raise
    
    def _load_main_config(self):
        """Load main configuration from environment variables"""
        config = {}
        
        # Telegram configuration
        config["telegram"] = {
            "bot_token": os.getenv("TELEGRAM_BOT_TOKEN"),
            "admin_user_id": int(os.getenv("TELEGRAM_ADMIN_USER_ID", 0))
        }
        
        # Exchange configuration
        config["exchanges"] = {
            "default_exchange": os.getenv("EXCHANGE", "kucoin"),
            "kucoin": {
                "api_key": os.getenv("KUCOIN_API_KEY"),
                "secret_key": os.getenv("KUCOIN_SECRET_KEY"),
                "passphrase": os.getenv("KUCOIN_PASSPHRASE"),
                "sandbox": os.getenv("KUCOIN_SANDBOX", "true").lower() == "true"
            },
            "mexc": {
                "api_key": os.getenv("MEXC_API_KEY"),
                "secret_key": os.getenv("MEXC_SECRET_KEY"),
                "sandbox": os.getenv("MEXC_SANDBOX", "true").lower() == "true"
            }
        }
        
        # Trading configuration
        config["trading"] = {
            "enabled": os.getenv("DEFAULT_TRADING_ENABLED", "true").lower() == "true",
            "mode": "test" if os.getenv("TEST_MODE", "true").lower() == "true" else "live",
            "daily_limit": float(os.getenv("DEFAULT_DAILY_LIMIT", "100.0")),
            "max_trades_per_day": int(os.getenv("MAX_TRADES_PER_DAY", "10")),
            "risk_per_trade": float(os.getenv("RISK_PER_TRADE", "0.02"))
        }
        
        # Security configuration
        config["security"] = {
            "rate_limit_enabled": os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true",
            "max_login_attempts": int(os.getenv("MAX_LOGIN_ATTEMPTS", "3")),
            "session_timeout": int(os.getenv("SESSION_TIMEOUT", "3600")),
            "enable_2fa": os.getenv("ENABLE_2FA", "false").lower() == "true"
        }
        
        # Database configuration
        config["database"] = {
            "url": os.getenv("DATABASE_URL", "sqlite:///trading_bot.db"),
            "backup_enabled": os.getenv("BACKUP_ENABLED", "true").lower() == "true",
            "backup_interval": int(os.getenv("BACKUP_INTERVAL_HOURS", "24"))
        }
        
        self.config_cache["main"] = config
    
    def _load_environment_config(self):
        """Load environment-specific configuration"""
        env_config_file = self.config_dir / f"{self.environment}.yaml"
        
        if env_config_file.exists():
            try:
                with open(env_config_file, 'r') as f:
                    env_config = yaml.safe_load(f)
                self.config_cache["environment"] = env_config
                logger.info(f"📁 Loaded environment config: {env_config_file}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load environment config: {e}")
        else:
            logger.info(f"📁 No environment config found for: {self.environment}")
    
    def _load_user_configs(self):
        """Load user-specific configurations"""
        user_configs_dir = self.config_dir / "user_configs"
        
        if user_configs_dir.exists():
            user_configs = {}
            for config_file in user_configs_dir.glob("*.json"):
                try:
                    with open(config_file, 'r') as f:
                        user_id = config_file.stem.replace("_config", "")
                        user_configs[user_id] = json.load(f)
                except Exception as e:
                    logger.warning(f"⚠️ Failed to load user config {config_file}: {e}")
            
            self.config_cache["users"] = user_configs
            logger.info(f"👥 Loaded {len(user_configs)} user configurations")
    
    def _validate_all_configs(self):
        """Validate all loaded configurations"""
        main_config = self.config_cache.get("main", {})
        
        for section_name, section_def in self.config_sections.items():
            section_config = main_config.get(section_name, {})
            self._validate_section(section_config, section_def)
    
    def _validate_section(self, config: Dict[str, Any], section_def: ConfigSection):
        """Validate a configuration section"""
        # Check required fields
        for field in section_def.required_fields:
            if field not in config or config[field] is None:
                raise ValueError(f"Required field '{field}' missing in {section_def.name} configuration")
        
        # Validate field values
        for field, value in config.items():
            if field in section_def.validation_rules:
                self._validate_field(field, value, section_def.validation_rules[field])
    
    def _validate_field(self, field_name: str, value: Any, rules: Dict[str, Any]):
        """Validate a single field value"""
        # Type validation
        if "type" in rules and not isinstance(value, rules["type"]):
            raise ValueError(f"Field '{field_name}' must be of type {rules['type'].__name__}")
        
        # Choice validation
        if "choices" in rules and value not in rules["choices"]:
            raise ValueError(f"Field '{field_name}' must be one of: {rules['choices']}")
        
        # Numeric validations
        if isinstance(value, (int, float)):
            if "min_value" in rules and value < rules["min_value"]:
                raise ValueError(f"Field '{field_name}' must be >= {rules['min_value']}")
            if "max_value" in rules and value > rules["max_value"]:
                raise ValueError(f"Field '{field_name}' must be <= {rules['max_value']}")
        
        # String validations
        if isinstance(value, str):
            if "min_length" in rules and len(value) < rules["min_length"]:
                raise ValueError(f"Field '{field_name}' must be at least {rules['min_length']} characters")
    
    def get_config(self, section: str, key: Optional[str] = None, user_id: Optional[str] = None):
        """Get configuration value with fallback hierarchy"""
        # Priority: user_config > environment_config > main_config
        
        # Try user-specific config first
        if user_id and "users" in self.config_cache:
            user_config = self.config_cache["users"].get(user_id, {})
            if section in user_config:
                if key:
                    if key in user_config[section]:
                        return user_config[section][key]
                else:
                    return user_config[section]
        
        # Try environment config
        if "environment" in self.config_cache:
            env_config = self.config_cache["environment"]
            if section in env_config:
                if key:
                    if key in env_config[section]:
                        return env_config[section][key]
                else:
                    return env_config[section]
        
        # Fallback to main config
        main_config = self.config_cache.get("main", {})
        if section in main_config:
            if key:
                return main_config[section].get(key)
            else:
                return main_config[section]
        
        return None
    
    def set_user_config(self, user_id: str, section: str, key: str, value: Any):
        """Set user-specific configuration"""
        if "users" not in self.config_cache:
            self.config_cache["users"] = {}
        
        if user_id not in self.config_cache["users"]:
            self.config_cache["users"][user_id] = {}
        
        if section not in self.config_cache["users"][user_id]:
            self.config_cache["users"][user_id][section] = {}
        
        self.config_cache["users"][user_id][section][key] = value
        
        # Save to file
        self._save_user_config(user_id)
    
    def _save_user_config(self, user_id: str):
        """Save user configuration to file"""
        user_configs_dir = self.config_dir / "user_configs"
        user_configs_dir.mkdir(exist_ok=True)
        
        config_file = user_configs_dir / f"{user_id}_config.json"
        
        try:
            with open(config_file, 'w') as f:
                json.dump(self.config_cache["users"][user_id], f, indent=2)
            logger.info(f"💾 Saved user config for: {user_id}")
        except Exception as e:
            logger.error(f"❌ Failed to save user config: {e}")
    
    def reload_config(self):
        """Reload all configurations"""
        logger.info("🔄 Reloading configurations...")
        self.config_cache.clear()
        self._load_all_configs()
    
    def get_all_configs(self) -> Dict[str, Any]:
        """Get all configurations for debugging"""
        return self.config_cache.copy()


# Global config manager instance
_config_manager = None

def get_config_manager() -> ConfigManager:
    """Get global config manager instance"""
    global _config_manager
    if _config_manager is None:
        environment = os.getenv("ENVIRONMENT", "development")
        _config_manager = ConfigManager(environment=environment)
    return _config_manager
