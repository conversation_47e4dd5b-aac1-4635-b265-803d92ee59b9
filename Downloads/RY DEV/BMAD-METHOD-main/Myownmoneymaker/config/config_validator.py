#!/usr/bin/env python3
"""
Configuration Validator voor Trading Bot
Uitgebreide validatie van alle configuratie-instellingen
"""

import os
import re
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """Result of configuration validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]


class ConfigValidator:
    """
    Comprehensive configuration validator
    
    Features:
    - Environment variable validation
    - API key format validation
    - Security settings validation
    - Trading parameter validation
    - Cross-validation between settings
    """
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.suggestions = []
    
    def validate_all(self) -> ValidationResult:
        """Validate all configuration aspects"""
        self.errors.clear()
        self.warnings.clear()
        self.suggestions.clear()
        
        # Core validations
        self._validate_telegram_config()
        self._validate_exchange_configs()
        self._validate_trading_config()
        self._validate_security_config()
        self._validate_database_config()
        
        # Cross-validations
        self._validate_mode_consistency()
        self._validate_security_level()
        
        # Performance suggestions
        self._suggest_optimizations()
        
        return ValidationResult(
            is_valid=len(self.errors) == 0,
            errors=self.errors.copy(),
            warnings=self.warnings.copy(),
            suggestions=self.suggestions.copy()
        )
    
    def _validate_telegram_config(self):
        """Validate Telegram bot configuration"""
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        admin_user_id = os.getenv("TELEGRAM_ADMIN_USER_ID")
        
        # Bot token validation
        if not bot_token:
            self.errors.append("TELEGRAM_BOT_TOKEN is required")
        elif not self._is_valid_telegram_token(bot_token):
            self.errors.append("TELEGRAM_BOT_TOKEN format is invalid")
        
        # Admin user ID validation
        if not admin_user_id:
            self.errors.append("TELEGRAM_ADMIN_USER_ID is required")
        else:
            try:
                admin_id = int(admin_user_id)
                if admin_id <= 0:
                    self.errors.append("TELEGRAM_ADMIN_USER_ID must be a positive integer")
            except ValueError:
                self.errors.append("TELEGRAM_ADMIN_USER_ID must be a valid integer")
    
    def _validate_exchange_configs(self):
        """Validate exchange configurations"""
        default_exchange = os.getenv("EXCHANGE", "kucoin").lower()
        
        # Validate default exchange
        valid_exchanges = ["kucoin", "mexc", "binance"]
        if default_exchange not in valid_exchanges:
            self.errors.append(f"EXCHANGE must be one of: {', '.join(valid_exchanges)}")
        
        # Validate KuCoin config
        self._validate_kucoin_config()
        
        # Validate MEXC config
        self._validate_mexc_config()
        
        # Check if at least one exchange is properly configured
        if not self._has_valid_exchange():
            self.errors.append("At least one exchange must be properly configured")
    
    def _validate_kucoin_config(self):
        """Validate KuCoin exchange configuration"""
        api_key = os.getenv("KUCOIN_API_KEY")
        secret_key = os.getenv("KUCOIN_SECRET_KEY")
        passphrase = os.getenv("KUCOIN_PASSPHRASE")
        sandbox = os.getenv("KUCOIN_SANDBOX", "true").lower()
        
        if api_key or secret_key or passphrase:  # If any KuCoin config is provided
            if not api_key:
                self.errors.append("KUCOIN_API_KEY is required when using KuCoin")
            elif not self._is_valid_kucoin_api_key(api_key):
                self.warnings.append("KUCOIN_API_KEY format looks suspicious")
            
            if not secret_key:
                self.errors.append("KUCOIN_SECRET_KEY is required when using KuCoin")
            
            if not passphrase:
                self.errors.append("KUCOIN_PASSPHRASE is required when using KuCoin")
            
            if sandbox not in ["true", "false"]:
                self.warnings.append("KUCOIN_SANDBOX should be 'true' or 'false'")
            elif sandbox == "false":
                self.warnings.append("⚠️ KuCoin LIVE MODE enabled - real money trading!")
    
    def _validate_mexc_config(self):
        """Validate MEXC exchange configuration"""
        api_key = os.getenv("MEXC_API_KEY")
        secret_key = os.getenv("MEXC_SECRET_KEY")
        sandbox = os.getenv("MEXC_SANDBOX", "true").lower()
        
        if api_key or secret_key:  # If any MEXC config is provided
            if not api_key:
                self.errors.append("MEXC_API_KEY is required when using MEXC")
            
            if not secret_key:
                self.errors.append("MEXC_SECRET_KEY is required when using MEXC")
            
            if sandbox not in ["true", "false"]:
                self.warnings.append("MEXC_SANDBOX should be 'true' or 'false'")
            elif sandbox == "false":
                self.warnings.append("⚠️ MEXC LIVE MODE enabled - real money trading!")
    
    def _validate_trading_config(self):
        """Validate trading configuration"""
        # Trading mode validation
        test_mode = os.getenv("TEST_MODE", "true").lower()
        live_mode = os.getenv("LIVE_MODE", "false").lower()
        
        if test_mode not in ["true", "false"]:
            self.warnings.append("TEST_MODE should be 'true' or 'false'")
        
        if live_mode not in ["true", "false"]:
            self.warnings.append("LIVE_MODE should be 'true' or 'false'")
        
        if test_mode == "false" and live_mode == "false":
            self.warnings.append("Neither TEST_MODE nor LIVE_MODE is enabled")
        
        if test_mode == "true" and live_mode == "true":
            self.errors.append("TEST_MODE and LIVE_MODE cannot both be true")
        
        # Trading limits validation
        self._validate_trading_limits()
        
        # Risk management validation
        self._validate_risk_management()
    
    def _validate_trading_limits(self):
        """Validate trading limits"""
        try:
            daily_limit = float(os.getenv("DEFAULT_DAILY_LIMIT", "100.0"))
            if daily_limit <= 0:
                self.errors.append("DEFAULT_DAILY_LIMIT must be positive")
            elif daily_limit > 10000:
                self.warnings.append("DEFAULT_DAILY_LIMIT is very high - consider reducing for safety")
        except ValueError:
            self.errors.append("DEFAULT_DAILY_LIMIT must be a valid number")
        
        try:
            max_trades = int(os.getenv("MAX_TRADES_PER_DAY", "10"))
            if max_trades <= 0:
                self.errors.append("MAX_TRADES_PER_DAY must be positive")
            elif max_trades > 100:
                self.warnings.append("MAX_TRADES_PER_DAY is very high - consider reducing")
        except ValueError:
            self.errors.append("MAX_TRADES_PER_DAY must be a valid integer")
        
        try:
            min_order = float(os.getenv("MIN_ORDER_SIZE", "5.0"))
            if min_order <= 0:
                self.errors.append("MIN_ORDER_SIZE must be positive")
        except ValueError:
            self.errors.append("MIN_ORDER_SIZE must be a valid number")
    
    def _validate_risk_management(self):
        """Validate risk management settings"""
        try:
            risk_per_trade = float(os.getenv("RISK_PER_TRADE", "0.02"))
            if risk_per_trade <= 0 or risk_per_trade > 1:
                self.errors.append("RISK_PER_TRADE must be between 0 and 1")
            elif risk_per_trade > 0.1:
                self.warnings.append("RISK_PER_TRADE > 10% is very risky")
        except ValueError:
            self.errors.append("RISK_PER_TRADE must be a valid number")
        
        try:
            stop_loss = float(os.getenv("STOP_LOSS_PERCENT", "0.05"))
            if stop_loss <= 0 or stop_loss > 1:
                self.errors.append("STOP_LOSS_PERCENT must be between 0 and 1")
        except ValueError:
            self.errors.append("STOP_LOSS_PERCENT must be a valid number")
        
        try:
            take_profit = float(os.getenv("TAKE_PROFIT_PERCENT", "0.15"))
            if take_profit <= 0:
                self.errors.append("TAKE_PROFIT_PERCENT must be positive")
        except ValueError:
            self.errors.append("TAKE_PROFIT_PERCENT must be a valid number")
    
    def _validate_security_config(self):
        """Validate security configuration"""
        # Rate limiting
        try:
            rate_limit = int(os.getenv("API_RATE_LIMIT", "10"))
            if rate_limit <= 0:
                self.errors.append("API_RATE_LIMIT must be positive")
            elif rate_limit > 100:
                self.warnings.append("API_RATE_LIMIT is very high - may hit exchange limits")
        except ValueError:
            self.errors.append("API_RATE_LIMIT must be a valid integer")
        
        # Login attempts
        try:
            max_attempts = int(os.getenv("MAX_LOGIN_ATTEMPTS", "3"))
            if max_attempts <= 0:
                self.errors.append("MAX_LOGIN_ATTEMPTS must be positive")
            elif max_attempts > 10:
                self.warnings.append("MAX_LOGIN_ATTEMPTS is very high - security risk")
        except ValueError:
            self.errors.append("MAX_LOGIN_ATTEMPTS must be a valid integer")
        
        # Session timeout
        try:
            timeout = int(os.getenv("SESSION_TIMEOUT", "3600"))
            if timeout < 300:
                self.warnings.append("SESSION_TIMEOUT < 5 minutes may cause frequent logouts")
            elif timeout > 86400:
                self.warnings.append("SESSION_TIMEOUT > 24 hours may be a security risk")
        except ValueError:
            self.errors.append("SESSION_TIMEOUT must be a valid integer")
        
        # Encryption key
        encryption_key = os.getenv("ENCRYPTION_KEY")
        if not encryption_key:
            self.warnings.append("ENCRYPTION_KEY not set - sensitive data will not be encrypted")
        elif len(encryption_key) < 32:
            self.warnings.append("ENCRYPTION_KEY should be at least 32 characters for security")
    
    def _validate_database_config(self):
        """Validate database configuration"""
        db_url = os.getenv("DATABASE_URL", "sqlite:///trading_bot.db")
        
        if not db_url:
            self.errors.append("DATABASE_URL is required")
        elif db_url.startswith("sqlite:///"):
            # SQLite specific validations
            db_path = db_url.replace("sqlite:///", "")
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                self.warnings.append(f"Database directory does not exist: {db_dir}")
        
        # Backup settings
        backup_enabled = os.getenv("BACKUP_ENABLED", "true").lower()
        if backup_enabled == "true":
            backup_location = os.getenv("BACKUP_LOCATION", "./backups/")
            if not os.path.exists(backup_location):
                self.warnings.append(f"Backup directory does not exist: {backup_location}")
    
    def _validate_mode_consistency(self):
        """Validate consistency between different mode settings"""
        test_mode = os.getenv("TEST_MODE", "true").lower() == "true"
        live_mode = os.getenv("LIVE_MODE", "false").lower() == "true"
        kucoin_sandbox = os.getenv("KUCOIN_SANDBOX", "true").lower() == "true"
        mexc_sandbox = os.getenv("MEXC_SANDBOX", "true").lower() == "true"
        
        if live_mode and (kucoin_sandbox or mexc_sandbox):
            self.warnings.append("LIVE_MODE enabled but exchanges are in sandbox mode")
        
        if test_mode and not (kucoin_sandbox and mexc_sandbox):
            self.warnings.append("TEST_MODE enabled but some exchanges are not in sandbox mode")
    
    def _validate_security_level(self):
        """Validate overall security level"""
        security_score = 0
        
        if os.getenv("ENCRYPTION_KEY"):
            security_score += 1
        
        if os.getenv("ENABLE_2FA", "false").lower() == "true":
            security_score += 1
        
        if int(os.getenv("MAX_LOGIN_ATTEMPTS", "3")) <= 5:
            security_score += 1
        
        if int(os.getenv("SESSION_TIMEOUT", "3600")) <= 7200:
            security_score += 1
        
        if security_score < 2:
            self.warnings.append("Security level is low - consider enabling more security features")
    
    def _suggest_optimizations(self):
        """Suggest performance and security optimizations"""
        # Performance suggestions
        if not os.getenv("HEALTH_CHECK_ENABLED", "true").lower() == "true":
            self.suggestions.append("Enable health checks for better monitoring")
        
        if not os.getenv("BACKUP_ENABLED", "true").lower() == "true":
            self.suggestions.append("Enable database backups for data safety")
        
        # Security suggestions
        if not os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true":
            self.suggestions.append("Enable rate limiting for API protection")
        
        if os.getenv("DEBUG_MODE", "false").lower() == "true":
            self.suggestions.append("Disable DEBUG_MODE in production for security")
    
    # Helper methods
    def _is_valid_telegram_token(self, token: str) -> bool:
        """Validate Telegram bot token format"""
        pattern = r'^\d{8,10}:[a-zA-Z0-9_-]{35}$'
        return bool(re.match(pattern, token))
    
    def _is_valid_kucoin_api_key(self, api_key: str) -> bool:
        """Validate KuCoin API key format"""
        return len(api_key) >= 24 and api_key.isalnum()
    
    def _has_valid_exchange(self) -> bool:
        """Check if at least one exchange is properly configured"""
        # Check KuCoin
        if (os.getenv("KUCOIN_API_KEY") and 
            os.getenv("KUCOIN_SECRET_KEY") and 
            os.getenv("KUCOIN_PASSPHRASE")):
            return True
        
        # Check MEXC
        if (os.getenv("MEXC_API_KEY") and 
            os.getenv("MEXC_SECRET_KEY")):
            return True
        
        return False


def validate_config() -> ValidationResult:
    """Convenience function to validate configuration"""
    validator = ConfigValidator()
    return validator.validate_all()


if __name__ == "__main__":
    # Run validation when script is executed directly
    from dotenv import load_dotenv
    load_dotenv()
    
    result = validate_config()
    
    print("🔍 Configuration Validation Results")
    print("=" * 50)
    
    if result.is_valid:
        print("✅ Configuration is valid!")
    else:
        print("❌ Configuration has errors!")
    
    if result.errors:
        print(f"\n🚨 Errors ({len(result.errors)}):")
        for error in result.errors:
            print(f"  • {error}")
    
    if result.warnings:
        print(f"\n⚠️ Warnings ({len(result.warnings)}):")
        for warning in result.warnings:
            print(f"  • {warning}")
    
    if result.suggestions:
        print(f"\n💡 Suggestions ({len(result.suggestions)}):")
        for suggestion in result.suggestions:
            print(f"  • {suggestion}")
