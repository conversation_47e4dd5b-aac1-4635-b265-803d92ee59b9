# Development Environment Configuration
# This file contains development-specific settings

# =============================================================================
# DEVELOPMENT ENVIRONMENT SETTINGS
# =============================================================================
environment: development
debug: true

# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================
telegram:
  # Development bot settings
  webhook_enabled: false
  polling_timeout: 30
  request_timeout: 10
  
  # Development-specific features
  debug_messages: true
  admin_commands_enabled: true
  test_user_simulation: true

# =============================================================================
# EXCHANGE CONFIGURATION
# =============================================================================
exchanges:
  # Force sandbox mode in development
  force_sandbox: true
  
  # Development-specific settings
  connection_timeout: 30
  request_timeout: 10
  retry_attempts: 3
  
  # Mock trading for development
  mock_trading_enabled: true
  mock_balance: 10000.0
  
  kucoin:
    sandbox: true
    rate_limit: 5  # Lower rate limit for development
    
  mexc:
    sandbox: true
    rate_limit: 5

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
trading:
  # Development trading settings
  mode: test
  paper_trading: true
  
  # Conservative limits for development
  daily_limit: 50.0
  max_trades_per_day: 5
  min_order_size: 1.0
  
  # Risk management for development
  risk_per_trade: 0.01  # 1% risk per trade
  max_position_size: 0.05  # 5% max position
  
  # Development-specific features
  trade_simulation: true
  backtesting_enabled: true
  strategy_testing: true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
database:
  # Development database settings
  url: "sqlite:///dev_trading_bot.db"
  echo: true  # Log all SQL queries
  
  # Development backup settings
  backup_enabled: true
  backup_interval: 1  # Backup every hour in development
  backup_retention_days: 7
  
  # Development-specific features
  auto_migrate: true
  seed_test_data: true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging:
  level: DEBUG
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Development log settings
  console_output: true
  file_output: true
  log_file: "logs/dev_bot.log"
  
  # Detailed logging for development
  log_sql_queries: true
  log_api_requests: true
  log_trade_details: true
  
  # Log rotation
  max_file_size: "10MB"
  backup_count: 5

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
security:
  # Relaxed security for development
  rate_limit_enabled: true
  max_requests_per_minute: 100
  
  # Development authentication
  session_timeout: 7200  # 2 hours
  max_login_attempts: 10
  
  # Development-specific security
  allow_insecure_connections: true
  skip_ssl_verification: false
  
  # CORS settings for development
  cors_enabled: true
  cors_origins: ["http://localhost:3000", "http://127.0.0.1:3000"]

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
monitoring:
  # Development monitoring settings
  health_check_enabled: true
  health_check_interval: 30
  
  # Performance monitoring
  performance_monitoring: true
  memory_monitoring: true
  cpu_monitoring: true
  
  # Development metrics
  detailed_metrics: true
  metrics_retention_days: 7
  
  # Alerting (disabled in development)
  alerts_enabled: false

# =============================================================================
# API CONFIGURATION
# =============================================================================
api:
  # Development API settings
  host: "127.0.0.1"
  port: 8000
  debug: true
  
  # Development-specific features
  auto_reload: true
  docs_enabled: true
  openapi_enabled: true
  
  # CORS for development
  cors_enabled: true
  cors_allow_credentials: true

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================
testing:
  # Test data settings
  use_test_data: true
  test_data_file: "data/test_market_data.json"
  
  # Mock services
  mock_exchanges: true
  mock_ai_analysis: true
  mock_notifications: true
  
  # Test automation
  automated_tests: true
  test_coverage_enabled: true
  
  # Performance testing
  load_testing_enabled: false
  stress_testing_enabled: false

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
development:
  # Hot reload settings
  hot_reload: true
  watch_files: true
  
  # Development utilities
  profiling_enabled: true
  memory_profiling: true
  
  # Code quality tools
  linting_enabled: true
  type_checking: true
  
  # Development dashboard
  dashboard_enabled: true
  dashboard_port: 8001
  
  # Debug tools
  debug_toolbar: true
  sql_debug: true
  
# =============================================================================
# FEATURE FLAGS
# =============================================================================
features:
  # Development feature flags
  new_ui_enabled: true
  advanced_analytics: true
  ai_predictions: false  # Disabled in development
  
  # Experimental features
  experimental_strategies: true
  beta_features: true
  
  # Development-only features
  dev_tools: true
  mock_mode: true
  test_mode: true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
notifications:
  # Development notification settings
  enabled: true
  
  # Channels
  telegram_enabled: true
  email_enabled: false
  webhook_enabled: false
  
  # Development-specific notifications
  debug_notifications: true
  verbose_notifications: true
  
  # Notification levels
  error_notifications: true
  warning_notifications: true
  info_notifications: false
  
  # Rate limiting
  rate_limit_enabled: true
  max_notifications_per_hour: 50
