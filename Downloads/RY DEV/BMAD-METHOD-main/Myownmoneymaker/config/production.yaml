# Production Environment Configuration
# This file contains production-specific settings

# =============================================================================
# PRODUCTION ENVIRONMENT SETTINGS
# =============================================================================
environment: production
debug: false

# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================
telegram:
  # Production bot settings
  webhook_enabled: true
  webhook_url: "${WEBHOOK_URL}"
  webhook_secret: "${WEBHOOK_SECRET}"
  
  # Production timeouts
  polling_timeout: 60
  request_timeout: 30
  
  # Production-specific features
  debug_messages: false
  admin_commands_enabled: true
  rate_limiting: true
  max_requests_per_second: 30

# =============================================================================
# EXCHANGE CONFIGURATION
# =============================================================================
exchanges:
  # Production exchange settings
  force_sandbox: false  # Allow live trading in production
  
  # Production connection settings
  connection_timeout: 60
  request_timeout: 30
  retry_attempts: 5
  retry_delay: 2
  
  # Production rate limiting
  rate_limit_enabled: true
  
  kucoin:
    sandbox: false  # Live trading
    rate_limit: 10
    max_orders_per_second: 5
    
  mexc:
    sandbox: false  # Live trading
    rate_limit: 10
    max_orders_per_second: 5

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
trading:
  # Production trading settings
  mode: live
  paper_trading: false
  
  # Production limits
  daily_limit: 1000.0
  max_trades_per_day: 20
  min_order_size: 10.0
  
  # Production risk management
  risk_per_trade: 0.02  # 2% risk per trade
  max_position_size: 0.1  # 10% max position
  stop_loss_percent: 0.05
  take_profit_percent: 0.15
  
  # Production safety features
  emergency_stop_enabled: true
  max_daily_loss: 0.05  # 5% max daily loss
  circuit_breaker_enabled: true
  
  # Advanced features
  trailing_stops_enabled: true
  position_scaling_enabled: true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
database:
  # Production database settings
  url: "${DATABASE_URL}"
  echo: false  # Don't log SQL queries in production
  
  # Production connection pool
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  
  # Production backup settings
  backup_enabled: true
  backup_interval: 6  # Backup every 6 hours
  backup_retention_days: 90
  backup_encryption: true
  
  # Production-specific features
  auto_migrate: false  # Manual migrations in production
  connection_monitoring: true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  
  # Production log settings
  console_output: false
  file_output: true
  log_file: "logs/production_bot.log"
  
  # Structured logging for production
  structured_logging: true
  json_format: true
  
  # Log rotation for production
  max_file_size: "100MB"
  backup_count: 10
  
  # Production log levels
  log_sql_queries: false
  log_api_requests: false
  log_trade_details: true
  log_errors: true
  
  # External logging
  syslog_enabled: false
  remote_logging: false

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
security:
  # Production security settings
  rate_limit_enabled: true
  max_requests_per_minute: 60
  
  # Production authentication
  session_timeout: 3600  # 1 hour
  max_login_attempts: 3
  account_lockout_duration: 1800  # 30 minutes
  
  # Production security features
  encryption_enabled: true
  ssl_required: true
  secure_cookies: true
  
  # API security
  api_key_required: true
  jwt_enabled: true
  jwt_expiry: 3600
  
  # Network security
  allowed_ips: []  # Empty means all IPs allowed
  firewall_enabled: false
  
  # Data protection
  data_encryption: true
  sensitive_data_masking: true

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
monitoring:
  # Production monitoring settings
  health_check_enabled: true
  health_check_interval: 60
  health_check_timeout: 10
  
  # Performance monitoring
  performance_monitoring: true
  memory_monitoring: true
  cpu_monitoring: true
  disk_monitoring: true
  
  # Production metrics
  metrics_enabled: true
  metrics_retention_days: 365
  detailed_metrics: false
  
  # Alerting
  alerts_enabled: true
  alert_channels: ["telegram", "email"]
  
  # Thresholds
  cpu_alert_threshold: 80
  memory_alert_threshold: 85
  disk_alert_threshold: 90
  error_rate_threshold: 5

# =============================================================================
# API CONFIGURATION
# =============================================================================
api:
  # Production API settings
  host: "0.0.0.0"
  port: 8000
  debug: false
  
  # Production features
  auto_reload: false
  docs_enabled: false  # Disable docs in production
  openapi_enabled: false
  
  # Production security
  cors_enabled: false
  cors_allow_credentials: false
  
  # Production performance
  workers: 4
  worker_timeout: 30
  keepalive: 2

# =============================================================================
# CACHING CONFIGURATION
# =============================================================================
caching:
  # Production caching settings
  enabled: true
  backend: "redis"
  redis_url: "${REDIS_URL}"
  
  # Cache settings
  default_timeout: 300
  key_prefix: "trading_bot:"
  
  # Cache strategies
  market_data_cache: 60
  user_data_cache: 300
  config_cache: 3600

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
notifications:
  # Production notification settings
  enabled: true
  
  # Channels
  telegram_enabled: true
  email_enabled: true
  webhook_enabled: true
  sms_enabled: false
  
  # Production notification levels
  error_notifications: true
  warning_notifications: true
  info_notifications: false
  trade_notifications: true
  
  # Rate limiting
  rate_limit_enabled: true
  max_notifications_per_hour: 20
  
  # Emergency notifications
  emergency_notifications: true
  emergency_contacts: ["${ADMIN_TELEGRAM_ID}"]

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
backup:
  # Production backup settings
  enabled: true
  schedule: "0 */6 * * *"  # Every 6 hours
  
  # Backup destinations
  local_backup: true
  cloud_backup: true
  cloud_provider: "aws_s3"
  
  # Backup retention
  local_retention_days: 30
  cloud_retention_days: 365
  
  # Backup encryption
  encryption_enabled: true
  compression_enabled: true
  
  # Recovery settings
  auto_recovery: false
  recovery_testing: true

# =============================================================================
# FEATURE FLAGS
# =============================================================================
features:
  # Production feature flags
  new_ui_enabled: false
  advanced_analytics: true
  ai_predictions: true
  
  # Experimental features (disabled in production)
  experimental_strategies: false
  beta_features: false
  
  # Production-only features
  live_trading: true
  real_money: true
  production_monitoring: true

# =============================================================================
# SCALING CONFIGURATION
# =============================================================================
scaling:
  # Auto-scaling settings
  auto_scaling: false
  min_instances: 1
  max_instances: 3
  
  # Load balancing
  load_balancer: false
  health_check_path: "/health"
  
  # Resource limits
  memory_limit: "2GB"
  cpu_limit: "1000m"

# =============================================================================
# COMPLIANCE & AUDIT
# =============================================================================
compliance:
  # Audit logging
  audit_logging: true
  audit_retention_days: 2555  # 7 years
  
  # Compliance features
  trade_reporting: true
  regulatory_compliance: true
  
  # Data retention
  data_retention_policy: true
  personal_data_retention_days: 2555
