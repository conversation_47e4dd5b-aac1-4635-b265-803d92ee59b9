"""
Paper Trading Manager voor risk-free trading simulation
"""
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger

@dataclass
class PaperOrder:
    """Paper trading order"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: Decimal
    price: Decimal
    order_type: str  # 'market' or 'limit'
    status: str = "pending"  # pending, filled, cancelled
    created_at: datetime = field(default_factory=datetime.now)
    filled_at: Optional[datetime] = None
    fee: Decimal = Decimal('0.001')  # 0.1% fee simulation

@dataclass
class PaperPosition:
    """Paper trading position"""
    symbol: str
    side: str
    amount: Decimal
    entry_price: Decimal
    current_price: Decimal
    unrealized_pnl: Decimal = Decimal('0')
    entry_time: datetime = field(default_factory=datetime.now)

@dataclass
class PaperBalance:
    """Paper trading balance"""
    asset: str
    free: Decimal
    locked: Decimal = Decimal('0')
    
    @property
    def total(self) -> Decimal:
        return self.free + self.locked

class PaperTradingManager:
    """
    Paper Trading Manager voor risk-free trading simulation
    
    Simuleert echte trading zonder echt geld te gebruiken.
    Perfect voor het testen van strategieën en het leren van trading.
    """
    
    def __init__(self, initial_balance: Decimal = Decimal('10000')):
        self.initial_balance = initial_balance
        self.balances: Dict[str, PaperBalance] = {}
        self.orders: Dict[str, PaperOrder] = {}
        self.positions: Dict[str, PaperPosition] = {}
        self.trade_history: List[Dict] = []
        
        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = Decimal('0')
        self.max_drawdown = Decimal('0')
        self.peak_balance = initial_balance
        
        # Data persistence
        self.data_file = Path("data/paper_trading.json")
        self.data_file.parent.mkdir(exist_ok=True)
        
        # Initialize with USDT balance
        self._initialize_balances()
        self._load_data()
        
        logger.info(f"📊 Paper Trading Manager initialized with ${initial_balance}")
    
    def _initialize_balances(self):
        """Initialize starting balances"""
        self.balances['USDT'] = PaperBalance(
            asset='USDT',
            free=self.initial_balance
        )
    
    def _load_data(self):
        """Load existing paper trading data"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    
                # Load balances
                for asset, balance_data in data.get('balances', {}).items():
                    self.balances[asset] = PaperBalance(
                        asset=asset,
                        free=Decimal(str(balance_data['free'])),
                        locked=Decimal(str(balance_data.get('locked', '0')))
                    )
                
                # Load trade history
                self.trade_history = data.get('trade_history', [])
                self.total_trades = data.get('total_trades', 0)
                self.winning_trades = data.get('winning_trades', 0)
                self.total_pnl = Decimal(str(data.get('total_pnl', '0')))
                
                logger.info(f"📊 Loaded paper trading data: {len(self.trade_history)} trades")
                
        except Exception as e:
            logger.error(f"❌ Error loading paper trading data: {e}")
    
    def _save_data(self):
        """Save paper trading data"""
        try:
            data = {
                'balances': {
                    asset: {
                        'asset': balance.asset,
                        'free': str(balance.free),
                        'locked': str(balance.locked)
                    } for asset, balance in self.balances.items()
                },
                'trade_history': self.trade_history,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'total_pnl': str(self.total_pnl),
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error saving paper trading data: {e}")
    
    async def create_order(self, symbol: str, side: str, amount: Decimal, 
                          price: Optional[Decimal] = None, order_type: str = "market") -> Optional[PaperOrder]:
        """
        Create a paper trading order
        
        Args:
            symbol: Trading pair (e.g., "BTC/USDT")
            side: "buy" or "sell"
            amount: Amount to trade
            price: Price for limit orders
            order_type: "market" or "limit"
        """
        try:
            # Generate order ID
            order_id = f"paper_{int(time.time() * 1000)}"
            
            # Get current market price if needed
            if order_type == "market" or not price:
                price = await self._get_market_price(symbol)
                if not price:
                    logger.error(f"❌ Could not get market price for {symbol}")
                    return None
            
            # Check if we have sufficient balance
            base_asset, quote_asset = symbol.split('/')
            
            if side == "buy":
                required_balance = amount * price
                if quote_asset not in self.balances or self.balances[quote_asset].free < required_balance:
                    logger.warning(f"⚠️ Insufficient {quote_asset} balance for buy order")
                    return None
            else:  # sell
                if base_asset not in self.balances or self.balances[base_asset].free < amount:
                    logger.warning(f"⚠️ Insufficient {base_asset} balance for sell order")
                    return None
            
            # Create order
            order = PaperOrder(
                id=order_id,
                symbol=symbol,
                side=side,
                amount=amount,
                price=price,
                order_type=order_type
            )
            
            # Execute order immediately for market orders
            if order_type == "market":
                await self._execute_order(order)
            
            self.orders[order_id] = order
            logger.info(f"📝 Paper order created: {side.upper()} {amount} {symbol} @ ${price}")
            
            return order
            
        except Exception as e:
            logger.error(f"❌ Error creating paper order: {e}")
            return None
    
    async def _execute_order(self, order: PaperOrder):
        """Execute a paper trading order"""
        try:
            base_asset, quote_asset = order.symbol.split('/')
            
            # Calculate fee
            fee_amount = order.amount * order.fee
            
            if order.side == "buy":
                # Deduct quote asset
                cost = order.amount * order.price
                if quote_asset in self.balances:
                    self.balances[quote_asset].free -= cost
                
                # Add base asset (minus fee)
                received_amount = order.amount - fee_amount
                if base_asset not in self.balances:
                    self.balances[base_asset] = PaperBalance(asset=base_asset, free=Decimal('0'))
                self.balances[base_asset].free += received_amount
                
            else:  # sell
                # Deduct base asset
                if base_asset in self.balances:
                    self.balances[base_asset].free -= order.amount
                
                # Add quote asset (minus fee)
                received = (order.amount * order.price) - (order.amount * order.price * order.fee)
                if quote_asset not in self.balances:
                    self.balances[quote_asset] = PaperBalance(asset=quote_asset, free=Decimal('0'))
                self.balances[quote_asset].free += received
            
            # Update order status
            order.status = "filled"
            order.filled_at = datetime.now()
            
            # Record trade
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'symbol': order.symbol,
                'side': order.side,
                'amount': str(order.amount),
                'price': str(order.price),
                'fee': str(fee_amount),
                'order_id': order.id,
                'type': 'paper_trade'
            }
            
            self.trade_history.append(trade_record)
            self.total_trades += 1
            
            # Save data
            self._save_data()
            
            logger.info(f"✅ Paper order executed: {order.side.upper()} {order.amount} {order.symbol} @ ${order.price}")
            
        except Exception as e:
            logger.error(f"❌ Error executing paper order: {e}")
    
    async def _get_market_price(self, symbol: str) -> Optional[Decimal]:
        """Get current market price for a symbol"""
        try:
            # This would normally fetch from exchange API
            # For now, return a simulated price
            prices = {
                'BTC/USDT': Decimal('43250.00'),
                'ETH/USDT': Decimal('2650.00'),
                'BNB/USDT': Decimal('315.50'),
                'ADA/USDT': Decimal('0.45'),
                'SOL/USDT': Decimal('95.50')
            }
            
            return prices.get(symbol, Decimal('100.00'))
            
        except Exception as e:
            logger.error(f"❌ Error getting market price: {e}")
            return None
    
    def get_balance(self, asset: str = None) -> Dict[str, Any]:
        """Get balance for specific asset or all assets"""
        if asset:
            balance = self.balances.get(asset)
            if balance:
                return {
                    'asset': balance.asset,
                    'free': float(balance.free),
                    'locked': float(balance.locked),
                    'total': float(balance.total)
                }
            return {'asset': asset, 'free': 0.0, 'locked': 0.0, 'total': 0.0}
        
        return {
            asset: {
                'asset': balance.asset,
                'free': float(balance.free),
                'locked': float(balance.locked),
                'total': float(balance.total)
            } for asset, balance in self.balances.items()
        }
    
    def get_portfolio_value(self) -> Dict[str, Any]:
        """Calculate total portfolio value in USDT"""
        try:
            total_value = Decimal('0')
            
            for asset, balance in self.balances.items():
                if asset == 'USDT':
                    total_value += balance.total
                else:
                    # Convert to USDT value (simplified)
                    # In real implementation, would fetch current prices
                    if balance.total > 0:
                        # Assume 1:1 for demo
                        total_value += balance.total
            
            pnl = total_value - self.initial_balance
            pnl_percentage = (pnl / self.initial_balance) * 100 if self.initial_balance > 0 else 0
            
            return {
                'total_value': float(total_value),
                'initial_balance': float(self.initial_balance),
                'pnl': float(pnl),
                'pnl_percentage': float(pnl_percentage),
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ Error calculating portfolio value: {e}")
            return {'total_value': 0.0, 'pnl': 0.0, 'pnl_percentage': 0.0}

# Global paper trading manager
_paper_trading_manager = None

def get_paper_trading_manager() -> PaperTradingManager:
    """Get global paper trading manager instance"""
    global _paper_trading_manager
    if _paper_trading_manager is None:
        _paper_trading_manager = PaperTradingManager()
    return _paper_trading_manager
