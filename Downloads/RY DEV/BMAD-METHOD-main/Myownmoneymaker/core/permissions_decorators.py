#!/usr/bin/env python3
"""
Permissions Decorators voor Trading Bot
Eenvoudige decorators voor permission checking in bot commands
"""

import functools
import logging
from typing import Callable, Any, Optional, List
from permissions_manager import get_permissions_manager, PermissionLevel, ResourceType

logger = logging.getLogger(__name__)


def require_permission(permission: str, ip_address_key: str = None):
    """
    Decorator to require specific permission for function execution

    Args:
        permission: Required permission name
        ip_address_key: Key in kwargs to get IP address from (optional)
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id from message or kwargs
            user_id = None
            ip_address = None

            # Try to get user_id from Telegram message
            if args and hasattr(args[0], "from_user"):
                user_id = str(args[0].from_user.id)
            elif "user_id" in kwargs:
                user_id = str(kwargs["user_id"])
            elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
                user_id = str(kwargs["message"].from_user.id)

            # Try to get IP address if specified
            if ip_address_key and ip_address_key in kwargs:
                ip_address = kwargs[ip_address_key]

            if not user_id:
                logger.error(f"Could not extract user_id for permission check: {permission}")
                return "❌ Authentication error"

            # Check permission
            permissions_manager = get_permissions_manager()
            has_perm, reason = permissions_manager.has_permission(user_id, permission, ip_address)

            if not has_perm:
                logger.warning(f"Permission denied for user {user_id}: {permission} - {reason}")
                return f"❌ Access denied: {reason}"

            # Permission granted, execute function
            return func(*args, **kwargs)

        return wrapper

    return decorator


def require_role(role: str):
    """
    Decorator to require specific role for function execution

    Args:
        role: Required role name
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id
            user_id = None
            if args and hasattr(args[0], "from_user"):
                user_id = str(args[0].from_user.id)
            elif "user_id" in kwargs:
                user_id = str(kwargs["user_id"])
            elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
                user_id = str(kwargs["message"].from_user.id)

            if not user_id:
                logger.error(f"Could not extract user_id for role check: {role}")
                return "❌ Authentication error"

            # Check role
            permissions_manager = get_permissions_manager()
            user_role = permissions_manager.get_user_role(user_id)

            if user_role != role:
                logger.warning(f"Role mismatch for user {user_id}: required {role}, has {user_role}")
                return f"❌ Access denied: Role '{role}' required"

            # Role matches, execute function
            return func(*args, **kwargs)

        return wrapper

    return decorator


def require_any_role(roles: List[str]):
    """
    Decorator to require any of the specified roles

    Args:
        roles: List of acceptable role names
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id
            user_id = None
            if args and hasattr(args[0], "from_user"):
                user_id = str(args[0].from_user.id)
            elif "user_id" in kwargs:
                user_id = str(kwargs["user_id"])
            elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
                user_id = str(kwargs["message"].from_user.id)

            if not user_id:
                logger.error(f"Could not extract user_id for role check: {roles}")
                return "❌ Authentication error"

            # Check role
            permissions_manager = get_permissions_manager()
            user_role = permissions_manager.get_user_role(user_id)

            if user_role not in roles:
                logger.warning(f"Role mismatch for user {user_id}: required one of {roles}, has {user_role}")
                return f"❌ Access denied: One of these roles required: {', '.join(roles)}"

            # Role matches, execute function
            return func(*args, **kwargs)

        return wrapper

    return decorator


def admin_only(func: Callable) -> Callable:
    """Decorator for admin-only functions"""
    return require_any_role(["admin", "super_admin"])(func)


def super_admin_only(func: Callable) -> Callable:
    """Decorator for super admin-only functions"""
    return require_role("super_admin")(func)


def trading_permission(func: Callable) -> Callable:
    """Decorator for trading-related functions"""
    return require_permission("trading.execute")(func)


def portfolio_permission(func: Callable) -> Callable:
    """Decorator for portfolio-related functions"""
    return require_permission("portfolio.view")(func)


def analytics_permission(func: Callable) -> Callable:
    """Decorator for analytics-related functions"""
    return require_permission("analytics.view")(func)


def emergency_permission(func: Callable) -> Callable:
    """Decorator for emergency functions"""
    return require_permission("emergency.access")(func)


def log_permission_usage(permission: str):
    """
    Decorator to log permission usage for audit purposes

    Args:
        permission: Permission being used
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id
            user_id = None
            if args and hasattr(args[0], "from_user"):
                user_id = str(args[0].from_user.id)
            elif "user_id" in kwargs:
                user_id = str(kwargs["user_id"])
            elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
                user_id = str(kwargs["message"].from_user.id)

            # Execute function
            result = func(*args, **kwargs)

            # Log usage
            if user_id:
                permissions_manager = get_permissions_manager()
                permissions_manager._log_permission_action(
                    user_id, "permission_used", permission, func.__name__, True, f"Function {func.__name__} executed"
                )

            return result

        return wrapper

    return decorator


def check_trading_limits(func: Callable) -> Callable:
    """
    Decorator to check trading limits before executing trading functions
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Extract user_id
        user_id = None
        if args and hasattr(args[0], "from_user"):
            user_id = str(args[0].from_user.id)
        elif "user_id" in kwargs:
            user_id = str(kwargs["user_id"])
        elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
            user_id = str(kwargs["message"].from_user.id)

        if not user_id:
            return "❌ Authentication error"

        # Get user role info
        permissions_manager = get_permissions_manager()
        user_role = permissions_manager.get_user_role(user_id)

        if not user_role:
            return "❌ No role assigned"

        role_info = permissions_manager.get_role_info(user_role)
        if not role_info:
            return "❌ Invalid role"

        # Check daily trade limit (this would need integration with trade tracking)
        max_daily_trades = role_info.get("max_daily_trades")
        if max_daily_trades is not None and max_daily_trades == 0:
            return "❌ Trading not allowed for your role"

        # Check position size limit (would need integration with position tracking)
        max_position_size = role_info.get("max_position_size")
        if max_position_size is not None and max_position_size == 0:
            return "❌ Position management not allowed for your role"

        # Check allowed trading pairs
        trading_pairs_allowed = role_info.get("trading_pairs_allowed", [])
        if trading_pairs_allowed:
            # Extract trading pair from kwargs if available
            trading_pair = kwargs.get("pair") or kwargs.get("symbol")
            if trading_pair and trading_pair not in trading_pairs_allowed:
                return f"❌ Trading pair {trading_pair} not allowed for your role"

        # All checks passed, execute function
        return func(*args, **kwargs)

    return wrapper


def auto_assign_role_on_first_use(default_role: str = "user"):
    """
    Decorator to automatically assign default role to new users

    Args:
        default_role: Role to assign to new users
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user_id
            user_id = None
            if args and hasattr(args[0], "from_user"):
                user_id = str(args[0].from_user.id)
            elif "user_id" in kwargs:
                user_id = str(kwargs["user_id"])
            elif "message" in kwargs and hasattr(kwargs["message"], "from_user"):
                user_id = str(kwargs["message"].from_user.id)

            if user_id:
                permissions_manager = get_permissions_manager()
                user_role = permissions_manager.get_user_role(user_id)

                # If user has no role, assign default role
                if not user_role:
                    success = permissions_manager.assign_role(user_id, default_role)
                    if success:
                        logger.info(f"Auto-assigned role '{default_role}' to new user {user_id}")
                    else:
                        logger.error(f"Failed to auto-assign role to user {user_id}")

            # Execute function
            return func(*args, **kwargs)

        return wrapper

    return decorator


# Convenience decorators for common permission combinations
def view_only(func: Callable) -> Callable:
    """Decorator for view-only functions"""
    return require_any_role(["viewer", "user", "trader", "analyst", "admin", "super_admin"])(func)


def trader_or_above(func: Callable) -> Callable:
    """Decorator for trader-level functions"""
    return require_any_role(["trader", "admin", "super_admin"])(func)


def analyst_or_above(func: Callable) -> Callable:
    """Decorator for analyst-level functions"""
    return require_any_role(["analyst", "admin", "super_admin"])(func)
