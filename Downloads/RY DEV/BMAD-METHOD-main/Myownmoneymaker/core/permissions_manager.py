#!/usr/bin/env python3
"""
Advanced Permissions & Roles Manager voor Trading Bot
Granulaire toegangscontrole met rol-gebaseerde permissies
"""

import sqlite3
import json
import logging
from typing import Dict, List, Set, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

logger = logging.getLogger(__name__)


class PermissionLevel(Enum):
    """Permission levels voor granulaire controle"""

    NONE = 0
    READ = 1
    WRITE = 2
    ADMIN = 3
    SUPER_ADMIN = 4


class ResourceType(Enum):
    """Resource types voor permission management"""

    TRADING = "trading"
    PORTFOLIO = "portfolio"
    ANALYTICS = "analytics"
    USER_MANAGEMENT = "user_management"
    SYSTEM_CONFIG = "system_config"
    AUDIT_LOGS = "audit_logs"
    EXCHANGE_CONFIG = "exchange_config"
    BOT_CONTROL = "bot_control"
    EMERGENCY = "emergency"


@dataclass
class Permission:
    """Individual permission definition"""

    name: str
    resource: ResourceType
    level: PermissionLevel
    description: str
    requires_2fa: bool = False
    time_restricted: bool = False
    allowed_hours: List[int] = field(default_factory=list)
    ip_restricted: bool = False
    allowed_ips: List[str] = field(default_factory=list)


@dataclass
class Role:
    """Role definition with permissions"""

    name: str
    display_name: str
    description: str
    permissions: Set[str] = field(default_factory=set)
    inherits_from: Optional[str] = None
    is_active: bool = True
    max_daily_trades: Optional[int] = None
    max_position_size: Optional[float] = None
    trading_pairs_allowed: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class UserPermissions:
    """User-specific permissions and restrictions"""

    user_id: str
    role: str
    custom_permissions: Set[str] = field(default_factory=set)
    denied_permissions: Set[str] = field(default_factory=set)
    temporary_permissions: Dict[str, datetime] = field(default_factory=dict)
    is_active: bool = True
    last_permission_check: datetime = field(default_factory=datetime.now)


class PermissionsManager:
    """
    Advanced Permissions & Roles Manager

    Features:
    - Granulaire permission controle
    - Rol-gebaseerde toegang (RBAC)
    - Tijdelijke permissies
    - IP en tijd restricties
    - 2FA requirements
    - Permission inheritance
    - Audit logging
    """

    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.permissions_cache = {}
        self.roles_cache = {}
        self.user_permissions_cache = {}

        # Initialize database and default data
        self._init_database()
        self._load_default_permissions()
        self._load_default_roles()

    def _init_database(self):
        """Initialize permissions database tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Permissions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) UNIQUE NOT NULL,
                resource VARCHAR(50) NOT NULL,
                level INTEGER NOT NULL,
                description TEXT,
                requires_2fa BOOLEAN DEFAULT 0,
                time_restricted BOOLEAN DEFAULT 0,
                allowed_hours TEXT,
                ip_restricted BOOLEAN DEFAULT 0,
                allowed_ips TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Roles table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100),
                description TEXT,
                permissions TEXT,
                inherits_from VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                max_daily_trades INTEGER,
                max_position_size DECIMAL(10,4),
                trading_pairs_allowed TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # User permissions table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR(50) UNIQUE NOT NULL,
                role VARCHAR(50) NOT NULL,
                custom_permissions TEXT,
                denied_permissions TEXT,
                temporary_permissions TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_permission_check TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (role) REFERENCES roles(name)
            )
        """)

        # Permission audit log
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permission_audit (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id VARCHAR(50),
                action VARCHAR(100),
                permission VARCHAR(100),
                resource VARCHAR(50),
                granted BOOLEAN,
                reason TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON user_permissions(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_user_id ON permission_audit(user_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_permission_audit_created_at ON permission_audit(created_at)")

        conn.commit()
        conn.close()
        logger.info("Permissions database initialized")

    def _load_default_permissions(self):
        """Load default permission definitions"""
        default_permissions = [
            # Trading permissions
            Permission("trading.view", ResourceType.TRADING, PermissionLevel.READ, "View trading data"),
            Permission("trading.execute", ResourceType.TRADING, PermissionLevel.WRITE, "Execute trades"),
            Permission("trading.manage", ResourceType.TRADING, PermissionLevel.ADMIN, "Manage trading settings"),
            Permission(
                "trading.emergency_stop",
                ResourceType.TRADING,
                PermissionLevel.SUPER_ADMIN,
                "Emergency stop trading",
                requires_2fa=True,
            ),
            # Portfolio permissions
            Permission("portfolio.view", ResourceType.PORTFOLIO, PermissionLevel.READ, "View portfolio"),
            Permission("portfolio.manage", ResourceType.PORTFOLIO, PermissionLevel.WRITE, "Manage portfolio"),
            # Analytics permissions
            Permission("analytics.view", ResourceType.ANALYTICS, PermissionLevel.READ, "View analytics"),
            Permission("analytics.advanced", ResourceType.ANALYTICS, PermissionLevel.WRITE, "Advanced analytics"),
            # User management permissions
            Permission("users.view", ResourceType.USER_MANAGEMENT, PermissionLevel.READ, "View users"),
            Permission("users.manage", ResourceType.USER_MANAGEMENT, PermissionLevel.ADMIN, "Manage users"),
            Permission(
                "users.create_admin",
                ResourceType.USER_MANAGEMENT,
                PermissionLevel.SUPER_ADMIN,
                "Create admin users",
                requires_2fa=True,
            ),
            # System configuration permissions
            Permission("system.view_config", ResourceType.SYSTEM_CONFIG, PermissionLevel.READ, "View system config"),
            Permission(
                "system.manage_config", ResourceType.SYSTEM_CONFIG, PermissionLevel.ADMIN, "Manage system config"
            ),
            Permission(
                "system.critical_config",
                ResourceType.SYSTEM_CONFIG,
                PermissionLevel.SUPER_ADMIN,
                "Critical system config",
                requires_2fa=True,
            ),
            # Audit permissions
            Permission("audit.view", ResourceType.AUDIT_LOGS, PermissionLevel.READ, "View audit logs"),
            Permission("audit.export", ResourceType.AUDIT_LOGS, PermissionLevel.ADMIN, "Export audit logs"),
            # Exchange configuration
            Permission("exchange.view", ResourceType.EXCHANGE_CONFIG, PermissionLevel.READ, "View exchange config"),
            Permission(
                "exchange.manage", ResourceType.EXCHANGE_CONFIG, PermissionLevel.ADMIN, "Manage exchange config"
            ),
            # Bot control
            Permission("bot.view_status", ResourceType.BOT_CONTROL, PermissionLevel.READ, "View bot status"),
            Permission("bot.restart", ResourceType.BOT_CONTROL, PermissionLevel.ADMIN, "Restart bot"),
            Permission(
                "bot.shutdown", ResourceType.BOT_CONTROL, PermissionLevel.SUPER_ADMIN, "Shutdown bot", requires_2fa=True
            ),
            # Emergency permissions
            Permission(
                "emergency.access",
                ResourceType.EMERGENCY,
                PermissionLevel.SUPER_ADMIN,
                "Emergency access",
                requires_2fa=True,
            ),
        ]

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for perm in default_permissions:
            cursor.execute(
                """
                INSERT OR IGNORE INTO permissions
                (name, resource, level, description, requires_2fa, time_restricted, allowed_hours, ip_restricted, allowed_ips)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    perm.name,
                    perm.resource.value,
                    perm.level.value,
                    perm.description,
                    perm.requires_2fa,
                    perm.time_restricted,
                    json.dumps(perm.allowed_hours),
                    perm.ip_restricted,
                    json.dumps(perm.allowed_ips),
                ),
            )

        conn.commit()
        conn.close()
        logger.info(f"Loaded {len(default_permissions)} default permissions")

    def _load_default_roles(self):
        """Load default role definitions"""
        default_roles = [
            Role(
                name="super_admin",
                display_name="Super Administrator",
                description="Full system access with all permissions",
                permissions={
                    "trading.view",
                    "trading.execute",
                    "trading.manage",
                    "trading.emergency_stop",
                    "portfolio.view",
                    "portfolio.manage",
                    "analytics.view",
                    "analytics.advanced",
                    "users.view",
                    "users.manage",
                    "users.create_admin",
                    "system.view_config",
                    "system.manage_config",
                    "system.critical_config",
                    "audit.view",
                    "audit.export",
                    "exchange.view",
                    "exchange.manage",
                    "bot.view_status",
                    "bot.restart",
                    "bot.shutdown",
                    "emergency.access",
                },
            ),
            Role(
                name="admin",
                display_name="Administrator",
                description="Administrative access with most permissions",
                permissions={
                    "trading.view",
                    "trading.execute",
                    "trading.manage",
                    "portfolio.view",
                    "portfolio.manage",
                    "analytics.view",
                    "analytics.advanced",
                    "users.view",
                    "users.manage",
                    "system.view_config",
                    "system.manage_config",
                    "audit.view",
                    "audit.export",
                    "exchange.view",
                    "exchange.manage",
                    "bot.view_status",
                    "bot.restart",
                },
                max_daily_trades=100,
                max_position_size=0.5,
            ),
            Role(
                name="trader",
                display_name="Advanced Trader",
                description="Advanced trading capabilities",
                permissions={
                    "trading.view",
                    "trading.execute",
                    "portfolio.view",
                    "portfolio.manage",
                    "analytics.view",
                    "analytics.advanced",
                    "bot.view_status",
                },
                max_daily_trades=50,
                max_position_size=0.2,
                trading_pairs_allowed=["BTC/USDT", "ETH/USDT", "ADA/USDT"],
            ),
            Role(
                name="user",
                display_name="Regular User",
                description="Basic trading and viewing permissions",
                permissions={"trading.view", "trading.execute", "portfolio.view", "analytics.view", "bot.view_status"},
                max_daily_trades=10,
                max_position_size=0.05,
                trading_pairs_allowed=["BTC/USDT", "ETH/USDT"],
            ),
            Role(
                name="viewer",
                display_name="Viewer",
                description="Read-only access",
                permissions={"trading.view", "portfolio.view", "analytics.view", "bot.view_status"},
                max_daily_trades=0,
                max_position_size=0.0,
            ),
            Role(
                name="analyst",
                display_name="Market Analyst",
                description="Analytics and research focused role",
                permissions={
                    "trading.view",
                    "portfolio.view",
                    "analytics.view",
                    "analytics.advanced",
                    "bot.view_status",
                },
                max_daily_trades=0,
                max_position_size=0.0,
            ),
        ]

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        for role in default_roles:
            cursor.execute(
                """
                INSERT OR REPLACE INTO roles
                (name, display_name, description, permissions, inherits_from, is_active,
                 max_daily_trades, max_position_size, trading_pairs_allowed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    role.name,
                    role.display_name,
                    role.description,
                    json.dumps(list(role.permissions)),
                    role.inherits_from,
                    role.is_active,
                    role.max_daily_trades,
                    role.max_position_size,
                    json.dumps(role.trading_pairs_allowed),
                ),
            )

        conn.commit()
        conn.close()
        logger.info(f"Loaded {len(default_roles)} default roles")

    def assign_role(self, user_id: str, role_name: str) -> bool:
        """Assign role to user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if role exists
            cursor.execute("SELECT name FROM roles WHERE name = ? AND is_active = 1", (role_name,))
            if not cursor.fetchone():
                logger.error(f"Role {role_name} does not exist or is inactive")
                return False

            # Insert or update user permissions
            cursor.execute(
                """
                INSERT OR REPLACE INTO user_permissions
                (user_id, role, custom_permissions, denied_permissions, temporary_permissions, is_active, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """,
                (user_id, role_name, json.dumps([]), json.dumps([]), json.dumps({}), True, datetime.now()),
            )

            conn.commit()
            conn.close()

            # Clear cache
            self.user_permissions_cache.pop(user_id, None)

            # Log the action
            self._log_permission_action(
                user_id, "role_assigned", role_name, "roles", True, f"Assigned role {role_name}"
            )

            logger.info(f"Assigned role {role_name} to user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Error assigning role: {e}")
            return False

    def has_permission(self, user_id: str, permission: str, ip_address: str = None) -> Tuple[bool, str]:
        """
        Check if user has specific permission

        Returns:
            Tuple[bool, str]: (has_permission, reason)
        """
        try:
            # Get user permissions
            user_perms = self._get_user_permissions(user_id)
            if not user_perms or not user_perms.is_active:
                return False, "User not found or inactive"

            # Check if permission is explicitly denied
            if permission in user_perms.denied_permissions:
                self._log_permission_action(
                    user_id, "permission_denied", permission, "explicit_deny", False, "Explicitly denied permission"
                )
                return False, "Permission explicitly denied"

            # Check custom permissions first
            if permission in user_perms.custom_permissions:
                return self._validate_permission_constraints(user_id, permission, ip_address)

            # Check temporary permissions
            if permission in user_perms.temporary_permissions:
                expiry = user_perms.temporary_permissions[permission]
                if datetime.now() < expiry:
                    return self._validate_permission_constraints(user_id, permission, ip_address)
                else:
                    # Remove expired permission
                    self._remove_temporary_permission(user_id, permission)

            # Check role permissions
            role_permissions = self._get_role_permissions(user_perms.role)
            if permission in role_permissions:
                return self._validate_permission_constraints(user_id, permission, ip_address)

            self._log_permission_action(
                user_id, "permission_denied", permission, "insufficient_privileges", False, "Insufficient privileges"
            )
            return False, "Insufficient privileges"

        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False, f"Error: {e}"

    def _validate_permission_constraints(
        self, user_id: str, permission: str, ip_address: str = None
    ) -> Tuple[bool, str]:
        """Validate permission constraints (time, IP, 2FA)"""
        try:
            # Get permission details
            perm_details = self._get_permission_details(permission)
            if not perm_details:
                return False, "Permission not found"

            # Check time restrictions
            if perm_details.get("time_restricted"):
                allowed_hours = json.loads(perm_details.get("allowed_hours", "[]"))
                current_hour = datetime.now().hour
                if allowed_hours and current_hour not in allowed_hours:
                    self._log_permission_action(
                        user_id,
                        "permission_denied",
                        permission,
                        "time_restriction",
                        False,
                        f"Access denied outside allowed hours",
                    )
                    return False, "Access denied outside allowed hours"

            # Check IP restrictions
            if perm_details.get("ip_restricted") and ip_address:
                allowed_ips = json.loads(perm_details.get("allowed_ips", "[]"))
                if allowed_ips and ip_address not in allowed_ips:
                    self._log_permission_action(
                        user_id,
                        "permission_denied",
                        permission,
                        "ip_restriction",
                        False,
                        f"Access denied from IP {ip_address}",
                    )
                    return False, "Access denied from this IP address"

            # Check 2FA requirement (placeholder - implement based on your 2FA system)
            if perm_details.get("requires_2fa"):
                # TODO: Implement 2FA check
                pass

            self._log_permission_action(
                user_id, "permission_granted", permission, "success", True, "Permission granted"
            )
            return True, "Permission granted"

        except Exception as e:
            logger.error(f"Error validating permission constraints: {e}")
            return False, f"Validation error: {e}"

    def grant_temporary_permission(self, user_id: str, permission: str, duration_hours: int = 24) -> bool:
        """Grant temporary permission to user"""
        try:
            expiry = datetime.now() + timedelta(hours=duration_hours)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get current temporary permissions
            cursor.execute("SELECT temporary_permissions FROM user_permissions WHERE user_id = ?", (user_id,))
            result = cursor.fetchone()

            if result:
                temp_perms = json.loads(result[0] or "{}")
                temp_perms[permission] = expiry.isoformat()

                cursor.execute(
                    """
                    UPDATE user_permissions
                    SET temporary_permissions = ?, updated_at = ?
                    WHERE user_id = ?
                """,
                    (json.dumps(temp_perms), datetime.now(), user_id),
                )

                conn.commit()
                conn.close()

                # Clear cache
                self.user_permissions_cache.pop(user_id, None)

                self._log_permission_action(
                    user_id,
                    "temporary_permission_granted",
                    permission,
                    "temporary",
                    True,
                    f"Granted for {duration_hours} hours",
                )
                logger.info(f"Granted temporary permission {permission} to user {user_id} for {duration_hours} hours")
                return True

            conn.close()
            return False

        except Exception as e:
            logger.error(f"Error granting temporary permission: {e}")
            return False

    def revoke_permission(self, user_id: str, permission: str) -> bool:
        """Revoke specific permission from user"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get current permissions
            cursor.execute(
                "SELECT custom_permissions, denied_permissions FROM user_permissions WHERE user_id = ?", (user_id,)
            )
            result = cursor.fetchone()

            if result:
                custom_perms = set(json.loads(result[0] or "[]"))
                denied_perms = set(json.loads(result[1] or "[]"))

                # Remove from custom permissions and add to denied
                custom_perms.discard(permission)
                denied_perms.add(permission)

                cursor.execute(
                    """
                    UPDATE user_permissions
                    SET custom_permissions = ?, denied_permissions = ?, updated_at = ?
                    WHERE user_id = ?
                """,
                    (json.dumps(list(custom_perms)), json.dumps(list(denied_perms)), datetime.now(), user_id),
                )

                conn.commit()
                conn.close()

                # Clear cache
                self.user_permissions_cache.pop(user_id, None)

                self._log_permission_action(
                    user_id, "permission_revoked", permission, "revoked", False, "Permission revoked"
                )
                logger.info(f"Revoked permission {permission} from user {user_id}")
                return True

            conn.close()
            return False

        except Exception as e:
            logger.error(f"Error revoking permission: {e}")
            return False

    def get_user_role(self, user_id: str) -> Optional[str]:
        """Get user's current role"""
        user_perms = self._get_user_permissions(user_id)
        return user_perms.role if user_perms else None

    def get_user_permissions_list(self, user_id: str) -> List[str]:
        """Get list of all permissions for user"""
        try:
            user_perms = self._get_user_permissions(user_id)
            if not user_perms:
                return []

            all_permissions = set()

            # Add role permissions
            role_permissions = self._get_role_permissions(user_perms.role)
            all_permissions.update(role_permissions)

            # Add custom permissions
            all_permissions.update(user_perms.custom_permissions)

            # Add temporary permissions (non-expired)
            current_time = datetime.now()
            for perm, expiry_str in user_perms.temporary_permissions.items():
                expiry = datetime.fromisoformat(expiry_str)
                if current_time < expiry:
                    all_permissions.add(perm)

            # Remove denied permissions
            all_permissions -= user_perms.denied_permissions

            return sorted(list(all_permissions))

        except Exception as e:
            logger.error(f"Error getting user permissions list: {e}")
            return []

    def get_role_info(self, role_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed role information"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT name, display_name, description, permissions, inherits_from,
                       is_active, max_daily_trades, max_position_size, trading_pairs_allowed
                FROM roles WHERE name = ?
            """,
                (role_name,),
            )

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    "name": result[0],
                    "display_name": result[1],
                    "description": result[2],
                    "permissions": json.loads(result[3] or "[]"),
                    "inherits_from": result[4],
                    "is_active": bool(result[5]),
                    "max_daily_trades": result[6],
                    "max_position_size": result[7],
                    "trading_pairs_allowed": json.loads(result[8] or "[]"),
                }

            return None

        except Exception as e:
            logger.error(f"Error getting role info: {e}")
            return None

    # Helper methods
    def _get_user_permissions(self, user_id: str) -> Optional[UserPermissions]:
        """Get user permissions from cache or database"""
        if user_id in self.user_permissions_cache:
            return self.user_permissions_cache[user_id]

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT user_id, role, custom_permissions, denied_permissions,
                       temporary_permissions, is_active, last_permission_check
                FROM user_permissions WHERE user_id = ?
            """,
                (user_id,),
            )

            result = cursor.fetchone()
            conn.close()

            if result:
                user_perms = UserPermissions(
                    user_id=result[0],
                    role=result[1],
                    custom_permissions=set(json.loads(result[2] or "[]")),
                    denied_permissions=set(json.loads(result[3] or "[]")),
                    temporary_permissions=json.loads(result[4] or "{}"),
                    is_active=bool(result[5]),
                    last_permission_check=datetime.fromisoformat(result[6]) if result[6] else datetime.now(),
                )

                # Cache the result
                self.user_permissions_cache[user_id] = user_perms
                return user_perms

            return None

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return None

    def _get_role_permissions(self, role_name: str) -> Set[str]:
        """Get permissions for a role"""
        if role_name in self.roles_cache:
            return self.roles_cache[role_name]

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT permissions FROM roles WHERE name = ? AND is_active = 1", (role_name,))
            result = cursor.fetchone()
            conn.close()

            if result:
                permissions = set(json.loads(result[0] or "[]"))
                self.roles_cache[role_name] = permissions
                return permissions

            return set()

        except Exception as e:
            logger.error(f"Error getting role permissions: {e}")
            return set()

    def _get_permission_details(self, permission_name: str) -> Optional[Dict[str, Any]]:
        """Get permission details from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                SELECT name, resource, level, description, requires_2fa,
                       time_restricted, allowed_hours, ip_restricted, allowed_ips
                FROM permissions WHERE name = ?
            """,
                (permission_name,),
            )

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    "name": result[0],
                    "resource": result[1],
                    "level": result[2],
                    "description": result[3],
                    "requires_2fa": bool(result[4]),
                    "time_restricted": bool(result[5]),
                    "allowed_hours": result[6],
                    "ip_restricted": bool(result[7]),
                    "allowed_ips": result[8],
                }

            return None

        except Exception as e:
            logger.error(f"Error getting permission details: {e}")
            return None

    def _remove_temporary_permission(self, user_id: str, permission: str):
        """Remove expired temporary permission"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT temporary_permissions FROM user_permissions WHERE user_id = ?", (user_id,))
            result = cursor.fetchone()

            if result:
                temp_perms = json.loads(result[0] or "{}")
                temp_perms.pop(permission, None)

                cursor.execute(
                    """
                    UPDATE user_permissions
                    SET temporary_permissions = ?, updated_at = ?
                    WHERE user_id = ?
                """,
                    (json.dumps(temp_perms), datetime.now(), user_id),
                )

                conn.commit()

            conn.close()

            # Clear cache
            self.user_permissions_cache.pop(user_id, None)

        except Exception as e:
            logger.error(f"Error removing temporary permission: {e}")

    def _log_permission_action(
        self,
        user_id: str,
        action: str,
        permission: str,
        resource: str,
        granted: bool,
        reason: str,
        ip_address: str = None,
        user_agent: str = None,
    ):
        """Log permission action for audit trail"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute(
                """
                INSERT INTO permission_audit
                (user_id, action, permission, resource, granted, reason, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (user_id, action, permission, resource, granted, reason, ip_address, user_agent),
            )

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error logging permission action: {e}")

    def get_audit_logs(self, user_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get permission audit logs"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if user_id:
                cursor.execute(
                    """
                    SELECT user_id, action, permission, resource, granted, reason,
                           ip_address, user_agent, created_at
                    FROM permission_audit
                    WHERE user_id = ?
                    ORDER BY created_at DESC
                    LIMIT ?
                """,
                    (user_id, limit),
                )
            else:
                cursor.execute(
                    """
                    SELECT user_id, action, permission, resource, granted, reason,
                           ip_address, user_agent, created_at
                    FROM permission_audit
                    ORDER BY created_at DESC
                    LIMIT ?
                """,
                    (limit,),
                )

            results = cursor.fetchall()
            conn.close()

            return [
                {
                    "user_id": row[0],
                    "action": row[1],
                    "permission": row[2],
                    "resource": row[3],
                    "granted": bool(row[4]),
                    "reason": row[5],
                    "ip_address": row[6],
                    "user_agent": row[7],
                    "created_at": row[8],
                }
                for row in results
            ]

        except Exception as e:
            logger.error(f"Error getting audit logs: {e}")
            return []

    def clear_cache(self):
        """Clear all caches"""
        self.permissions_cache.clear()
        self.roles_cache.clear()
        self.user_permissions_cache.clear()
        logger.info("Permissions cache cleared")


# Global permissions manager instance
_permissions_manager = None


def get_permissions_manager() -> PermissionsManager:
    """Get global permissions manager instance"""
    global _permissions_manager
    if _permissions_manager is None:
        _permissions_manager = PermissionsManager()
    return _permissions_manager
