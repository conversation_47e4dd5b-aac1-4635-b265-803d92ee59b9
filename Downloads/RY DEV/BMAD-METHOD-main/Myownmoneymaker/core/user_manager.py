"""
User Manager for multi-user trading bot.
Handles user registration, authentication, and authorization.
"""


class UserManager:
    def __init__(self):
        self.users = {}

    def register_user(self, user_id, api_key, secret_key):
        """Register a new user with API credentials."""
        self.users[user_id] = {"api_key": api_key, "secret_key": secret_key}

    def authenticate_user(self, user_id):
        """Check if a user is registered."""
        return user_id in self.users

    def get_user_credentials(self, user_id):
        """Retrieve API credentials for a user."""
        return self.users.get(user_id, None)
