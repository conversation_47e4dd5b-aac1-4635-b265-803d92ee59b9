version: '3.8'

services:
  trading-bot:
    build: .
    container_name: myownmoneymaker-bot
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    environment:
      - TZ=Europe/Amsterdam
    networks:
      - trading-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getMe', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  trading-network:
    driver: bridge
