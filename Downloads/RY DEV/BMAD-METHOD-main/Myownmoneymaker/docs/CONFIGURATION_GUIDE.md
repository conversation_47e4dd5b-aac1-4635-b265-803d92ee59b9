# 🔧 Trading Bot Configuration Guide

Een uitgebreide gids voor het configureren van je crypto trading bot.

## 📋 Inhoudsopgave

1. [Quick Start](#quick-start)
2. [Configuratie Structuur](#configuratie-structuur)
3. [Environment Setup](#environment-setup)
4. [Exchange Configuratie](#exchange-configuratie)
5. [Trading Instellingen](#trading-instellingen)
6. [Beveiliging](#beveiliging)
7. [Monitoring](#monitoring)
8. [Troubleshooting](#troubleshooting)

## 🚀 Quick Start

### Automatische Setup (Aanbevolen)

```bash
# Voer de interactieve setup uit
python scripts/setup_config.py

# Test je configuratie
python scripts/test_config.py

# Start de bot
python simple_bot.py
```

### Handmatige Setup

```bash
# Kopieer environment template
cp .env.example .env

# Bewerk .env met je instellingen
nano .env

# Valideer configuratie
python config/config_validator.py
```

## 🏗️ Configuratie Structuur

```
config/
├── config_manager.py      # Centraal configuratie management
├── config_validator.py    # Configuratie validatie
├── development.yaml       # Development environment
├── production.yaml        # Production environment
└── user_configs/          # Gebruiker-specifieke configs
    └── user1_config.json
```

### Environment Hiërarchie

1. **User Config** (hoogste prioriteit)
2. **Environment Config** (development.yaml, production.yaml)
3. **Main Config** (.env file)
4. **Defaults** (laagste prioriteit)

## 🌍 Environment Setup

### Beschikbare Environments

- **development**: Voor ontwikkeling en testing
- **staging**: Voor pre-production testing
- **production**: Voor live trading

### Environment Variabelen

```bash
# Basis environment
ENVIRONMENT=development
DEBUG_MODE=true
TEST_MODE=true
LIVE_MODE=false
```

## 📱 Telegram Configuratie

### Bot Token Verkrijgen

1. Start een chat met [@BotFather](https://t.me/BotFather)
2. Stuur `/newbot`
3. Volg de instructies
4. Kopieer je bot token

### User ID Verkrijgen

1. Start een chat met [@userinfobot](https://t.me/userinfobot)
2. Kopieer je user ID

### Configuratie

```bash
TELEGRAM_BOT_TOKEN=*********0:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_ADMIN_USER_ID=*********
```

## 💱 Exchange Configuratie

### KuCoin Setup

1. Ga naar [KuCoin API Management](https://www.kucoin.com/account/api)
2. Maak een nieuwe API key
3. Stel permissies in: `General`, `Trade`
4. Kopieer API credentials

```bash
KUCOIN_API_KEY=your_api_key
KUCOIN_SECRET_KEY=your_secret_key
KUCOIN_PASSPHRASE=your_passphrase
KUCOIN_SANDBOX=true  # Voor testing
```

### MEXC Setup

1. Ga naar [MEXC API Management](https://www.mexc.com/user/api)
2. Maak een nieuwe API key
3. Stel permissies in: `Spot Trading`

```bash
MEXC_API_KEY=your_api_key
MEXC_SECRET_KEY=your_secret_key
MEXC_SANDBOX=true  # Voor testing
```

### Binance Setup (Optioneel)

1. Ga naar [Binance API Management](https://www.binance.com/en/my/settings/api-management)
2. Maak een nieuwe API key
3. Stel permissies in: `Enable Spot & Margin Trading`

```bash
BINANCE_API_KEY=your_api_key
BINANCE_SECRET_KEY=your_secret_key
BINANCE_TESTNET=true  # Voor testing
```

## 📈 Trading Instellingen

### Basis Trading Config

```bash
# Trading mode
EXCHANGE=kucoin
USE_TESTNET=true
LIVE_MODE=false

# Trading limits
DEFAULT_DAILY_LIMIT=100.00
MAX_TRADES_PER_DAY=10
MIN_ORDER_SIZE=5.00

# Risk management
RISK_PER_TRADE=0.02          # 2% per trade
MAX_POSITION_SIZE=0.1        # 10% max position
STOP_LOSS_PERCENT=0.05       # 5% stop loss
TAKE_PROFIT_PERCENT=0.15     # 15% take profit
```

### Geavanceerde Trading Config

```bash
# Position management
MAX_OPEN_POSITIONS=5
POSITION_SIZING_METHOD=risk_based
MAX_DAILY_LOSS=0.05

# Trailing stops
TRAILING_STOP_ENABLED=false
TRAILING_STOP_PERCENT=0.02

# Position scaling
POSITION_SCALING_ENABLED=false
SCALE_IN_LEVELS=0.01,0.02,0.03
SCALE_OUT_LEVELS=0.02,0.03,0.05
```

## 🔒 Beveiliging

### Encryption Key Genereren

```python
import secrets
encryption_key = secrets.token_urlsafe(32)
print(f"ENCRYPTION_KEY={encryption_key}")
```

### Security Settings

```bash
# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key

# Rate limiting
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=60

# Authentication
MAX_LOGIN_ATTEMPTS=3
SESSION_TIMEOUT=3600
ENABLE_2FA=false

# API security
API_RATE_LIMIT=10
```

### Best Practices

- ✅ Gebruik altijd encryption in production
- ✅ Start met sandbox/testnet mode
- ✅ Gebruik sterke API key permissies
- ✅ Monitor je API key usage
- ❌ Deel nooit je API keys
- ❌ Commit nooit .env naar git

## 🗄️ Database Configuratie

### SQLite (Default)

```bash
DATABASE_URL=sqlite:///trading_bot.db
BACKUP_ENABLED=true
BACKUP_INTERVAL_HOURS=24
```

### PostgreSQL (Production)

```bash
DATABASE_URL=postgresql://user:password@localhost:5432/trading_bot
```

### Backup Settings

```bash
BACKUP_ENABLED=true
BACKUP_LOCATION=./backups/
KEEP_BACKUPS_DAYS=30
AUTO_BACKUP_ENABLED=true
```

## 📊 Monitoring

### Health Checks

```bash
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=60
PERFORMANCE_MONITORING=true
```

### Logging

```bash
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log
```

### Notifications

```bash
ENABLE_NOTIFICATIONS=true
ADMIN_NOTIFICATIONS=true
USER_NOTIFICATIONS=true
TRADE_NOTIFICATIONS=true
ERROR_NOTIFICATIONS=true
```

## 🔧 User-Specific Configuratie

### User Config Bestand

Locatie: `config/user_configs/{user_id}_config.json`

```json
{
  "trading": {
    "daily_limit": 50.0,
    "risk_per_trade": 0.01,
    "max_trades_per_day": 5
  },
  "notifications": {
    "trade_alerts": true,
    "error_alerts": true
  },
  "preferences": {
    "language": "nl",
    "timezone": "Europe/Amsterdam"
  }
}
```

### Programmatisch User Config

```python
from config.config_manager import get_config_manager

config_manager = get_config_manager()

# Set user-specific setting
config_manager.set_user_config("*********", "trading", "daily_limit", 200.0)

# Get user-specific setting
daily_limit = config_manager.get_config("trading", "daily_limit", user_id="*********")
```

## 🧪 Testing & Validatie

### Configuratie Testen

```bash
# Volledige configuratie test
python scripts/test_config.py

# Alleen validatie
python config/config_validator.py

# Specifieke exchange test
python tests/test_exchange_connections.py
```

### Validatie Resultaten

- ✅ **Success**: Configuratie is correct
- ⚠️ **Warning**: Configuratie werkt maar heeft aandachtspunten
- ❌ **Error**: Configuratie moet worden gecorrigeerd

## 🚨 Troubleshooting

### Veelvoorkomende Problemen

#### Bot Token Errors

```
❌ Invalid bot token format
```

**Oplossing**: Controleer of je bot token het juiste format heeft (10 cijfers:35 karakters)

#### Exchange Connection Errors

```
❌ KuCoin: Connection failed - Invalid API key
```

**Oplossing**: 
1. Controleer API key, secret en passphrase
2. Controleer API key permissies
3. Controleer of sandbox mode correct is ingesteld

#### Database Errors

```
❌ Database connection failed
```

**Oplossing**:
1. Controleer of database directory bestaat
2. Controleer database URL format
3. Controleer schrijfrechten

### Debug Mode

Voor gedetailleerde logging:

```bash
DEBUG_MODE=true
LOG_LEVEL=DEBUG
```

### Support

- 📚 Documentatie: `docs/`
- 🐛 Issues: Check logs in `logs/`
- 💬 Community: README.md voor contact info

## 📝 Configuration Checklist

### Voor Development

- [ ] `.env` bestand aangemaakt
- [ ] Telegram bot token ingesteld
- [ ] Admin user ID ingesteld
- [ ] Exchange API keys ingesteld (sandbox mode)
- [ ] TEST_MODE=true
- [ ] Configuratie gevalideerd
- [ ] Connection tests geslaagd

### Voor Production

- [ ] Production environment configuratie
- [ ] Encryption key ingesteld
- [ ] Live API keys ingesteld (met juiste permissies)
- [ ] LIVE_MODE=true (alleen als je echt wilt live traden)
- [ ] Backup ingeschakeld
- [ ] Monitoring ingeschakeld
- [ ] Security settings gecontroleerd
- [ ] Rate limiting ingeschakeld

## 🔄 Configuration Updates

### Hot Reload

```python
from config.config_manager import get_config_manager

config_manager = get_config_manager()
config_manager.reload_config()  # Herlaad alle configuraties
```

### Environment Switching

```bash
# Switch naar production
export ENVIRONMENT=production

# Restart bot om nieuwe environment te laden
```

---

**⚠️ Belangrijk**: Start altijd met test mode en sandbox exchanges voordat je live gaat traden!
