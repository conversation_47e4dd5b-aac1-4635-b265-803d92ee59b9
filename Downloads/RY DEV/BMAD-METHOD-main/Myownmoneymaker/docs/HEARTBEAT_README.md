# 💓 Heartbeat Monitor

Een monitoring systeem dat de status van je trading bot bewaakt en regelmatig updates stuurt naar Telegram admins.

## 🎯 Functionaliteit

### ✅ **Wat het doet:**
- **Process Monitoring**: Checkt elke 60 seconden of `main.py` nog draait
- **Trade Tracking**: Haalt laatste trade informatie op uit `core/trade_manager.py`
- **Status Reporting**: Stuurt uptime, status en strategie-namen naar Telegram admins
- **System Metrics**: Monitort CPU, geheugen en disk gebruik
- **Alert System**: Stuurt waarschuwingen bij problemen

### 📊 **Heartbeat Berichten bevatten:**
- ⏱️ **Uptime** van de bot
- 🤖 **Status** (Actief/Gestopt/Problemen)
- 📈 **Aantal trades** vandaag
- 🎯 **Actieve strategieën**
- 🔄 **Laatste trade** informatie
- 💻 **Systeem metrics**

## 🚀 Installatie & Gebruik

### **1. Vereisten**
```bash
pip install psutil aiohttp loguru
```

### **2. Configuratie**
Zorg dat je `.env` bestand de volgende instellingen heeft:
```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_USER_ID=123456789,987654321
```

### **3. Starten**
```bash
# Handmatig starten
python3 heartbeat_monitor.py

# Of gebruik het startup script
./start_heartbeat.sh
```

### **4. Als achtergrond service**
```bash
# Start in background
nohup python3 heartbeat_monitor.py > logs/heartbeat.log 2>&1 &

# Check if running
ps aux | grep heartbeat_monitor
```

## 📁 Bestandsstructuur

```
├── heartbeat_monitor.py      # Hoofdbestand
├── start_heartbeat.sh        # Startup script
├── bot/
│   └── notify.py            # TelegramNotifier klasse
├── core/
│   └── trade_manager.py     # Trade informatie
├── config/
│   └── settings.py          # Configuratie
└── logs/
    └── heartbeat.log        # Monitor logs
```

## 🔧 Configuratie Opties

### **Heartbeat Interval**
```python
# In heartbeat_monitor.py
self.heartbeat_interval = 60  # seconds (standaard: 60)
```

### **Monitoring Thresholds**
```python
# CPU warning threshold
if cpu_percent > 80:
    return "🟡 Hoog CPU gebruik"

# Memory warning threshold  
elif memory_mb > 500:  # 500MB
    return "🟡 Hoog geheugengebruik"
```

## 📱 Telegram Berichten

### **💓 Heartbeat Bericht**
```
💓 Bot Heartbeat

⏱️ Uptime: 2:34:15
🤖 Status: 🟢 Actief
📊 Trades Vandaag: 5

🎯 Actieve Strategieën:
Day Trading, Scalping, Momentum

🔄 Laatste Trade: BTC/USDT buy @ 14:25:30

📅 Timestamp: 2024-01-15 14:30:00
```

### **🚨 Alert Berichten**
```
🚨 ALERT: SYSTEM

🔴 HIGH PRIORITY

Bot status: 🔴 Gestopt

📅 Time: 2024-01-15 14:30:00
```

## 🛠️ Troubleshooting

### **Monitor start niet**
```bash
# Check Python path
which python3

# Check dependencies
python3 -c "import psutil, aiohttp, loguru"

# Check permissions
ls -la heartbeat_monitor.py
```

### **Geen Telegram berichten**
```bash
# Test bot token
curl "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"

# Check admin user IDs
echo $TELEGRAM_ADMIN_USER_ID

# Check logs
tail -f logs/heartbeat.log
```

### **Process niet gevonden**
```bash
# Check if main.py is running
ps aux | grep main.py

# Check process name
ps aux | grep python
```

## 📊 Monitoring Dashboard

### **System Metrics**
- 💻 **CPU Usage**: Percentage CPU gebruik
- 🧠 **Memory**: RAM gebruik in MB en percentage  
- 💾 **Disk**: Disk ruimte gebruik
- 🌐 **Network**: Data verzonden/ontvangen

### **Trading Metrics**
- 📈 **Total Trades**: Totaal aantal trades
- 🎯 **Win Rate**: Percentage succesvolle trades
- 💰 **Daily P&L**: Dagelijkse winst/verlies
- ⏱️ **Last Trade**: Tijd van laatste trade

## 🔄 Automatisch Herstarten

### **Systemd Service (Linux)**
```bash
# Create service file
sudo nano /etc/systemd/system/heartbeat-monitor.service

[Unit]
Description=Trading Bot Heartbeat Monitor
After=network.target

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/your/bot
ExecStart=/usr/bin/python3 heartbeat_monitor.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target

# Enable and start
sudo systemctl enable heartbeat-monitor
sudo systemctl start heartbeat-monitor
```

### **Cron Job**
```bash
# Edit crontab
crontab -e

# Add line to check every 5 minutes
*/5 * * * * /path/to/your/bot/check_heartbeat.sh
```

## 📝 Logs

### **Log Locaties**
- `logs/heartbeat.log` - Monitor activiteit
- `logs/trading_bot.log` - Bot activiteit  
- `logs/errors.log` - Error logs

### **Log Levels**
- `INFO` - Normale activiteit
- `WARNING` - Waarschuwingen
- `ERROR` - Fouten
- `DEBUG` - Debug informatie

## 🔐 Beveiliging

### **API Token Beveiliging**
- Gebruik environment variables
- Geen tokens in code
- Roteer tokens regelmatig

### **Access Control**
- Alleen admin users krijgen berichten
- Rate limiting op Telegram API
- Secure credential storage

## 🎯 Volgende Stappen

1. **Test de monitor** met `python3 heartbeat_monitor.py`
2. **Configureer alerts** voor kritieke events
3. **Setup automatisch herstarten** met systemd
4. **Monitor de logs** voor problemen
5. **Pas thresholds aan** naar jouw behoeften

## 📞 Support

Bij problemen:
1. Check de logs in `logs/heartbeat.log`
2. Verificeer Telegram bot token
3. Test handmatig met `python3 heartbeat_monitor.py`
4. Check process permissions en dependencies
