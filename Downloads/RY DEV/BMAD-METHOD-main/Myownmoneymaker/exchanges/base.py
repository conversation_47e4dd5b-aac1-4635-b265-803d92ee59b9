"""
Base exchange interface
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal

@dataclass
class Balance:
    """Account balance for a specific currency"""
    currency: str
    free: Decimal
    used: Decimal
    total: Decimal

@dataclass
class Order:
    """Trading order"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: Decimal
    price: Decimal
    status: str
    timestamp: int
    filled: Decimal = Decimal('0')
    remaining: Decimal = Decimal('0')
    cost: Decimal = Decimal('0')
    fee: Optional[Dict] = None

@dataclass
class Ticker:
    """Price ticker information"""
    symbol: str
    bid: Decimal
    ask: Decimal
    last: Decimal
    high: Decimal
    low: Decimal
    volume: Decimal
    timestamp: int

class BaseExchange(ABC):
    """Base class for all exchange implementations"""
    
    def __init__(self, api_key: str, secret_key: str, passphrase: Optional[str] = None, sandbox: bool = False):
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        self.sandbox = sandbox
        self.exchange = None
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the exchange"""
        pass
    
    @abstractmethod
    async def get_balance(self) -> Dict[str, Balance]:
        """Get account balances"""
        pass
    
    @abstractmethod
    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker information for a symbol"""
        pass
    
    @abstractmethod
    async def create_market_buy_order(self, symbol: str, amount: Decimal) -> Order:
        """Create a market buy order"""
        pass
    
    @abstractmethod
    async def create_market_sell_order(self, symbol: str, amount: Decimal) -> Order:
        pass
    
    @abstractmethod
    async def create_limit_buy_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create a limit buy order"""
        pass
    
    @abstractmethod
    async def create_limit_sell_order(self, symbol: str, amount: Decimal, price: Decimal) -> Order:
        """Create a limit sell order"""
        pass
    
    @abstractmethod
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """Get open orders"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str, symbol: str) -> Order:
        """Get order status"""
        pass
