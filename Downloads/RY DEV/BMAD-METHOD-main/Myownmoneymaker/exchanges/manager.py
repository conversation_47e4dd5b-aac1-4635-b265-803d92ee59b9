"""
Exchange manager for handling multiple exchanges
"""

from typing import Dict, List, Optional
from decimal import Decimal
from .base import BaseExchange, Balance, Order, Ticker
from .kucoin import <PERSON><PERSON>oinExchange
from .mexc import MEXCExchange

# Import from config directory
from config.settings import Settings
from loguru import logger


class ExchangeManager:
    """Manages multiple exchanges"""

    def __init__(self):
        self.exchanges: Dict[str, BaseExchange] = {}
        self.settings = Settings()
        self._initialize_exchanges()

    def _initialize_exchanges(self):
        """Initialize all configured exchanges"""
        # Initialize KuCoin
        try:
            self.exchanges["kucoin"] = KuCoinExchange(
                api_key=self.settings.kucoin_api_key,
                secret_key=self.settings.kucoin_secret_key,
                passphrase=self.settings.kucoin_passphrase,
                sandbox=self.settings.kucoin_sandbox,
            )
            logger.info("KuCoin exchange initialized")
        except Exception as e:
            logger.error(f"Failed to initialize KuCoin: {e}")

        # Initialize MEXC
        try:
            self.exchanges["mexc"] = MEXCExchange(
                api_key=self.settings.mexc_api_key,
                secret_key=self.settings.mexc_secret_key,
                sandbox=self.settings.mexc_sandbox,
            )
            logger.info("MEXC exchange initialized")
        except Exception as e:
            logger.error(f"Failed to initialize MEXC: {e}")

    async def _connect_exchanges(self):
        """Connect to all initialized exchanges"""
        for name, exchange in self.exchanges.items():
            try:
                logger.info(f"Connecting to {name}...")
                connected = await exchange.connect()
                if connected:
                    logger.info(f"✅ Successfully connected to {name}")
                else:
                    logger.error(f"❌ Failed to connect to {name}")
            except Exception as e:
                logger.error(f"❌ Error connecting to {name}: {e}")

    async def connect_all(self) -> Dict[str, bool]:
        """Connect to all exchanges"""
        results = {}
        for name, exchange in self.exchanges.items():
            try:
                results[name] = await exchange.connect()
            except Exception as e:
                logger.error(f"Failed to connect to {name}: {e}")
                results[name] = False
        return results

    async def initialize(self):
        """Initialize exchange connections."""
        await self._connect_exchanges()

    async def close_all(self):
        """Close all exchange connections."""
        # Add cleanup logic here
        pass

    def get_exchange(self, exchange_name: str) -> Optional[BaseExchange]:
        """Get exchange by name"""
        return self.exchanges.get(exchange_name.lower())

    def list_exchanges(self) -> List[str]:
        """List available exchanges"""
        return list(self.exchanges.keys())

    async def get_all_balances(self) -> Dict[str, Dict[str, Balance]]:
        """Get balances from all exchanges"""
        all_balances = {}
        for name, exchange in self.exchanges.items():
            try:
                all_balances[name] = await exchange.get_balance()
            except Exception as e:
                logger.error(f"Failed to get balance from {name}: {e}")
                all_balances[name] = {}
        return all_balances

    async def get_ticker_from_all(self, symbol: str) -> Dict[str, Ticker]:
        """Get ticker from all exchanges"""
        tickers = {}
        for name, exchange in self.exchanges.items():
            try:
                tickers[name] = await exchange.get_ticker(symbol)
            except Exception as e:
                logger.error(f"Failed to get ticker for {symbol} from {name}: {e}")
        return tickers

    async def find_best_price(self, symbol: str, side: str) -> Optional[tuple]:
        """Find best price across exchanges"""
        tickers = await self.get_ticker_from_all(symbol)

        if not tickers:
            return None

        best_exchange = None
        best_price = None

        for exchange_name, ticker in tickers.items():
            if side.lower() == "buy":
                # For buying, we want the lowest ask price
                price = ticker.ask
                if best_price is None or (price > 0 and price < best_price):
                    best_price = price
                    best_exchange = exchange_name
            else:
                # For selling, we want the highest bid price
                price = ticker.bid
                if best_price is None or price > best_price:
                    best_price = price
                    best_exchange = exchange_name

        return (best_exchange, best_price) if best_exchange else None

    async def create_order(
        self,
        exchange_name: str,
        order_type: str,
        side: str,
        symbol: str,
        amount: Decimal,
        price: Optional[Decimal] = None,
    ) -> Order:
        """Create order on specified exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError(f"Exchange {exchange_name} not found")

        if order_type.lower() == "market":
            if side.lower() == "buy":
                return await exchange.create_market_buy_order(symbol, amount)
            else:
                return await exchange.create_market_sell_order(symbol, amount)
        elif order_type.lower() == "limit":
            if price is None:
                raise ValueError("Price is required for limit orders")
            if side.lower() == "buy":
                return await exchange.create_limit_buy_order(symbol, amount, price)
            else:
                return await exchange.create_limit_sell_order(symbol, amount, price)
        else:
            raise ValueError(f"Unsupported order type: {order_type}")

    async def get_all_open_orders(self) -> Dict[str, List[Order]]:
        """Get open orders from all exchanges"""
        all_orders = {}
        for name, exchange in self.exchanges.items():
            try:
                all_orders[name] = await exchange.get_open_orders()
            except Exception as e:
                logger.error(f"Failed to get open orders from {name}: {e}")
                all_orders[name] = []
        return all_orders

    async def cancel_order(self, exchange_name: str, order_id: str, symbol: str) -> bool:
        """Cancel order on specified exchange"""
        exchange = self.get_exchange(exchange_name)
        if not exchange:
            raise ValueError(f"Exchange {exchange_name} not found")

        return await exchange.cancel_order(order_id, symbol)
