"""
Gamification System for Trading Bot
Handles XP, levels, achievements, leaderboards
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

class GamificationManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize gamification tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # User XP and Level table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_gamification (
                user_id INTEGER PRIMARY KEY,
                xp INTEGER DEFAULT 0,
                level INTEGER DEFAULT 1,
                total_trades INTEGER DEFAULT 0,
                successful_trades INTEGER DEFAULT 0,
                total_profit REAL DEFAULT 0.0,
                streak_days INTEGER DEFAULT 0,
                last_activity DATE,
                achievements TEXT DEFAULT '[]',
                badges TEXT DEFAULT '[]',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Leaderboard table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leaderboard (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT,
                period TEXT, -- 'daily', 'weekly', 'monthly'
                metric TEXT, -- 'xp', 'profit', 'trades'
                value REAL,
                rank INTEGER,
                date DATE,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Achievements definitions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS achievements (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                icon TEXT,
                requirement_type TEXT, -- 'trades', 'profit', 'streak', 'xp'
                requirement_value INTEGER,
                xp_reward INTEGER DEFAULT 0,
                badge_reward TEXT,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Initialize default achievements
        self.init_default_achievements()
    
    def init_default_achievements(self):
        """Initialize default achievements"""
        default_achievements = [
            {
                'id': 'first_trade',
                'name': 'Eerste Trade',
                'description': 'Voltooi je eerste trade',
                'icon': '🎯',
                'requirement_type': 'trades',
                'requirement_value': 1,
                'xp_reward': 100,
                'badge_reward': 'Beginner Trader'
            },
            {
                'id': 'trade_master_10',
                'name': 'Trade Master',
                'description': 'Voltooi 10 trades',
                'icon': '🏆',
                'requirement_type': 'trades',
                'requirement_value': 10,
                'xp_reward': 500,
                'badge_reward': 'Trade Master'
            },
            {
                'id': 'profit_maker',
                'name': 'Profit Maker',
                'description': 'Maak €100 winst',
                'icon': '💰',
                'requirement_type': 'profit',
                'requirement_value': 100,
                'xp_reward': 1000,
                'badge_reward': 'Profit Maker'
            },
            {
                'id': 'streak_warrior',
                'name': 'Streak Warrior',
                'description': '7 dagen achter elkaar actief',
                'icon': '🔥',
                'requirement_type': 'streak',
                'requirement_value': 7,
                'xp_reward': 750,
                'badge_reward': 'Streak Warrior'
            },
            {
                'id': 'level_10',
                'name': 'Veteraan',
                'description': 'Bereik level 10',
                'icon': '⭐',
                'requirement_type': 'level',
                'requirement_value': 10,
                'xp_reward': 2000,
                'badge_reward': 'Veteraan'
            }
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for achievement in default_achievements:
            cursor.execute('''
                INSERT OR IGNORE INTO achievements 
                (id, name, description, icon, requirement_type, requirement_value, xp_reward, badge_reward)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                achievement['id'], achievement['name'], achievement['description'],
                achievement['icon'], achievement['requirement_type'], achievement['requirement_value'],
                achievement['xp_reward'], achievement['badge_reward']
            ))
        
        conn.commit()
        conn.close()
    
    def get_user_gamification(self, user_id: int) -> Dict:
        """Get user's gamification data"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM user_gamification WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'user_id': result[0],
                'xp': result[1],
                'level': result[2],
                'total_trades': result[3],
                'successful_trades': result[4],
                'total_profit': result[5],
                'streak_days': result[6],
                'last_activity': result[7],
                'achievements': json.loads(result[8]) if result[8] else [],
                'badges': json.loads(result[9]) if result[9] else []
            }
        else:
            # Create new user gamification record
            return self.create_user_gamification(user_id)
    
    def create_user_gamification(self, user_id: int) -> Dict:
        """Create new user gamification record"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_gamification (user_id, last_activity)
            VALUES (?, ?)
        ''', (user_id, datetime.now().date()))
        
        conn.commit()
        conn.close()
        
        return {
            'user_id': user_id,
            'xp': 0,
            'level': 1,
            'total_trades': 0,
            'successful_trades': 0,
            'total_profit': 0.0,
            'streak_days': 0,
            'last_activity': datetime.now().date(),
            'achievements': [],
            'badges': []
        }
    
    def add_xp(self, user_id: int, xp_amount: int, reason: str = "") -> Dict:
        """Add XP to user and check for level up"""
        user_data = self.get_user_gamification(user_id)
        new_xp = user_data['xp'] + xp_amount
        new_level = self.calculate_level(new_xp)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE user_gamification 
            SET xp = ?, level = ?, last_activity = ?
            WHERE user_id = ?
        ''', (new_xp, new_level, datetime.now().date(), user_id))
        
        conn.commit()
        conn.close()
        
        level_up = new_level > user_data['level']
        
        return {
            'xp_gained': xp_amount,
            'total_xp': new_xp,
            'level': new_level,
            'level_up': level_up,
            'reason': reason
        }
    
    def calculate_level(self, xp: int) -> int:
        """Calculate level based on XP (exponential curve)"""
        if xp < 100:
            return 1
        
        # Level formula: level = floor(sqrt(xp/100)) + 1
        import math
        return int(math.sqrt(xp / 100)) + 1
    
    def xp_for_next_level(self, current_level: int) -> int:
        """Calculate XP needed for next level"""
        return (current_level ** 2) * 100
    
    def record_trade(self, user_id: int, profit: float, is_successful: bool) -> List[Dict]:
        """Record a trade and check for achievements"""
        user_data = self.get_user_gamification(user_id)
        
        # Update trade statistics
        new_total_trades = user_data['total_trades'] + 1
        new_successful_trades = user_data['successful_trades'] + (1 if is_successful else 0)
        new_total_profit = user_data['total_profit'] + profit
        
        # Calculate XP reward
        base_xp = 50  # Base XP per trade
        profit_xp = max(0, int(profit * 10))  # 10 XP per euro profit
        success_bonus = 25 if is_successful else 0
        total_xp = base_xp + profit_xp + success_bonus
        
        # Update database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE user_gamification 
            SET total_trades = ?, successful_trades = ?, total_profit = ?, last_activity = ?
            WHERE user_id = ?
        ''', (new_total_trades, new_successful_trades, new_total_profit, datetime.now().date(), user_id))
        
        conn.commit()
        conn.close()
        
        # Add XP
        xp_result = self.add_xp(user_id, total_xp, f"Trade voltooid (+€{profit:.2f})")
        
        # Check for achievements
        achievements = self.check_achievements(user_id)
        
        return {
            'xp_result': xp_result,
            'achievements': achievements,
            'trade_stats': {
                'total_trades': new_total_trades,
                'successful_trades': new_successful_trades,
                'success_rate': (new_successful_trades / new_total_trades) * 100,
                'total_profit': new_total_profit
            }
        }
    
    def check_achievements(self, user_id: int) -> List[Dict]:
        """Check if user has earned any new achievements"""
        user_data = self.get_user_gamification(user_id)
        current_achievements = set(user_data['achievements'])
        new_achievements = []
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM achievements WHERE is_active = 1')
        all_achievements = cursor.fetchall()
        
        for achievement in all_achievements:
            achievement_id = achievement[0]
            
            # Skip if already earned
            if achievement_id in current_achievements:
                continue
            
            # Check if requirement is met
            requirement_type = achievement[4]
            requirement_value = achievement[5]
            
            earned = False
            
            if requirement_type == 'trades':
                earned = user_data['total_trades'] >= requirement_value
            elif requirement_type == 'profit':
                earned = user_data['total_profit'] >= requirement_value
            elif requirement_type == 'streak':
                earned = user_data['streak_days'] >= requirement_value
            elif requirement_type == 'level':
                earned = user_data['level'] >= requirement_value
            elif requirement_type == 'xp':
                earned = user_data['xp'] >= requirement_value
            
            if earned:
                # Award achievement
                new_achievements.append({
                    'id': achievement_id,
                    'name': achievement[1],
                    'description': achievement[2],
                    'icon': achievement[3],
                    'xp_reward': achievement[6],
                    'badge_reward': achievement[7]
                })
                
                # Update user achievements
                current_achievements.add(achievement_id)
                
                # Add badge if applicable
                if achievement[7]:
                    user_badges = set(user_data['badges'])
                    user_badges.add(achievement[7])
                    user_data['badges'] = list(user_badges)
                
                # Award XP
                if achievement[6] > 0:
                    self.add_xp(user_id, achievement[6], f"Achievement: {achievement[1]}")
        
        # Update user achievements in database
        if new_achievements:
            cursor.execute('''
                UPDATE user_gamification 
                SET achievements = ?, badges = ?
                WHERE user_id = ?
            ''', (json.dumps(list(current_achievements)), json.dumps(user_data['badges']), user_id))
            
            conn.commit()
        
        conn.close()
        return new_achievements
    
    def get_leaderboard(self, period: str = 'weekly', metric: str = 'xp', limit: int = 10) -> List[Dict]:
        """Get leaderboard for specified period and metric"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if metric == 'xp':
            cursor.execute('''
                SELECT ug.user_id, u.username, ug.xp, ug.level
                FROM user_gamification ug
                LEFT JOIN users u ON ug.user_id = u.telegram_id
                ORDER BY ug.xp DESC
                LIMIT ?
            ''', (limit,))
        elif metric == 'profit':
            cursor.execute('''
                SELECT ug.user_id, u.username, ug.total_profit, ug.total_trades
                FROM user_gamification ug
                LEFT JOIN users u ON ug.user_id = u.telegram_id
                ORDER BY ug.total_profit DESC
                LIMIT ?
            ''', (limit,))
        elif metric == 'trades':
            cursor.execute('''
                SELECT ug.user_id, u.username, ug.total_trades, ug.successful_trades
                FROM user_gamification ug
                LEFT JOIN users u ON ug.user_id = u.telegram_id
                ORDER BY ug.total_trades DESC
                LIMIT ?
            ''', (limit,))
        
        results = cursor.fetchall()
        conn.close()
        
        leaderboard = []
        for i, result in enumerate(results, 1):
            leaderboard.append({
                'rank': i,
                'user_id': result[0],
                'username': result[1] or f"User{result[0]}",
                'value': result[2],
                'extra': result[3] if len(result) > 3 else None
            })
        
        return leaderboard
    
    def update_daily_streak(self, user_id: int) -> Dict:
        """Update user's daily activity streak"""
        user_data = self.get_user_gamification(user_id)
        today = datetime.now().date()
        last_activity = datetime.strptime(user_data['last_activity'], '%Y-%m-%d').date() if user_data['last_activity'] else None
        
        if last_activity:
            days_diff = (today - last_activity).days
            
            if days_diff == 1:
                # Consecutive day
                new_streak = user_data['streak_days'] + 1
            elif days_diff == 0:
                # Same day
                new_streak = user_data['streak_days']
            else:
                # Streak broken
                new_streak = 1
        else:
            new_streak = 1
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE user_gamification 
            SET streak_days = ?, last_activity = ?
            WHERE user_id = ?
        ''', (new_streak, today, user_id))
        
        conn.commit()
        conn.close()
        
        return {
            'streak_days': new_streak,
            'streak_bonus_xp': min(new_streak * 10, 100)  # Max 100 XP bonus
        }
