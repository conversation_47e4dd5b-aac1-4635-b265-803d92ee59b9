"""
Verbeterde Telegram bot handler met gestructureerde command handling
"""
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from loguru import logger
from config.settings import Settings
from core.security import SecurityManager, require_auth, rate_limit
from core.logging_config import log_trade, log_performance

@dataclass
class TelegramMessage:
    """Telegram message data structure"""
    chat_id: int
    user_id: int
    message_id: int
    text: str
    timestamp: float

@dataclass
class CallbackQuery:
    """Telegram callback query data structure"""
    query_id: str
    chat_id: int
    user_id: int
    message_id: int
    data: str

class CommandHandler:
    """
    Base class voor command handlers
    """
    def __init__(self, name: str, description: str, admin_only: bool = False):
        self.name = name
        self.description = description
        self.admin_only = admin_only

    async def handle(self, message: TelegramMessage, args: List[str], bot: 'TelegramBotHandler') -> str:
        """Handle the command"""
        raise NotImplementedError

class TelegramBotHandler:
    """
    Verbeterde Telegram bot handler met modular command system

    Deze klasse beheert alle Telegram bot functionaliteit op een
    gestructureerde manier met proper error handling en security.
    """

    def __init__(self, settings: Settings = None):
        self.settings = settings or Settings()
        self.bot_token = self.settings.telegram_bot_token
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        self.session: Optional[aiohttp.ClientSession] = None
        self.running = False

        # Command handlers
        self.commands: Dict[str, CommandHandler] = {}
        self.callback_handlers: Dict[str, Callable] = {}

        # Security
        self.security_manager = SecurityManager(self.settings.encryption_key)

        # User state tracking
        self.user_states: Dict[int, Dict[str, Any]] = {}

        # Rate limiting
        self.message_counts: Dict[int, List[float]] = {}

        # Initialize default commands
        self._register_default_commands()

    def _register_default_commands(self):
        """Registreer standaard commands"""
        from .commands import (
            StartCommand, HelpCommand, PingCommand, BalanceCommand,
            PriceCommand, AnalysisCommand, TradingCommand,
            PaperTradingCommand, PerformanceCommand
        )

        self.register_command(StartCommand())
        self.register_command(HelpCommand())
        self.register_command(PingCommand())
        self.register_command(BalanceCommand())
        self.register_command(PriceCommand())
        self.register_command(AnalysisCommand())
        self.register_command(TradingCommand())
        self.register_command(PaperTradingCommand())
        self.register_command(PerformanceCommand())

    def register_command(self, handler: CommandHandler):
        """Registreer een command handler"""
        self.commands[handler.name] = handler
        logger.debug(f"Registered command: /{handler.name}")

    def register_callback_handler(self, pattern: str, handler: Callable):
        """Registreer een callback handler"""
        self.callback_handlers[pattern] = handler
        logger.debug(f"Registered callback handler: {pattern}")

    async def initialize(self) -> bool:
        """Initialize bot connection"""
        try:
            # Create HTTP session
            connector = aiohttp.TCPConnector(ssl=False)
            self.session = aiohttp.ClientSession(connector=connector)

            # Test bot token
            url = f"{self.base_url}/getMe"
            async with self.session.get(url) as response:
                result = await response.json()
                if result.get('ok'):
                    bot_info = result['result']
                    logger.info(f"✅ Bot connected: @{bot_info['username']}")
                    return True
                else:
                    logger.error("❌ Invalid bot token")
                    return False

        except Exception as e:
            logger.error(f"❌ Error initializing bot: {e}")
            return False

    @rate_limit(max_calls=30, window_seconds=60)
    async def send_message(self, chat_id: int, text: str,
                          reply_markup: Optional[Dict] = None,
                          parse_mode: str = "Markdown") -> bool:
        """Send message to Telegram chat"""
        try:
            url = f"{self.base_url}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": text,
                "parse_mode": parse_mode
            }

            if reply_markup:
                data["reply_markup"] = json.dumps(reply_markup)

            async with self.session.post(url, json=data) as response:
                result = await response.json()
                return result.get('ok', False)

        except Exception as e:
            logger.error(f"❌ Error sending message: {e}")
            return False

    @rate_limit(max_calls=20, window_seconds=60)
    async def edit_message(self, chat_id: int, message_id: int, text: str,
                          reply_markup: Optional[Dict] = None) -> bool:
        """Edit existing message"""
        try:
            url = f"{self.base_url}/editMessageText"
            data = {
                "chat_id": chat_id,
                "message_id": message_id,
                "text": text,
                "parse_mode": "Markdown"
            }

            if reply_markup:
                data["reply_markup"] = json.dumps(reply_markup)

            async with self.session.post(url, json=data) as response:
                result = await response.json()
                return result.get('ok', False)

        except Exception as e:
            logger.error(f"❌ Error editing message: {e}")
            return False

    async def answer_callback_query(self, query_id: str, text: str = "") -> bool:
        """Answer callback query"""
        try:
            url = f"{self.base_url}/answerCallbackQuery"
            data = {
                "callback_query_id": query_id,
                "text": text
            }

            async with self.session.post(url, json=data) as response:
                result = await response.json()
                return result.get('ok', False)

        except Exception as e:
            logger.error(f"❌ Error answering callback: {e}")
            return False

    def _check_rate_limit(self, user_id: int) -> bool:
        """Check rate limiting voor gebruiker"""
        import time
        current_time = time.time()

        if user_id not in self.message_counts:
            self.message_counts[user_id] = []

        # Clean old messages (older than 1 minute)
        self.message_counts[user_id] = [
            msg_time for msg_time in self.message_counts[user_id]
            if current_time - msg_time < 60
        ]

        # Check if under limit (max 20 messages per minute)
        if len(self.message_counts[user_id]) >= 20:
            return False

        # Add current message
        self.message_counts[user_id].append(current_time)
        return True

    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        return self.settings.is_authorized_user(user_id)

    async def handle_message(self, message_data: Dict):
        """Handle incoming message"""
        try:
            # Parse message
            chat_id = message_data['chat']['id']
            user_id = message_data['from']['id']
            message_id = message_data['message_id']
            text = message_data.get('text', '')

            # Rate limiting
            if not self._check_rate_limit(user_id):
                await self.send_message(chat_id, "⚠️ Te veel berichten. Wacht even...")
                return

            # Create message object
            message = TelegramMessage(
                chat_id=chat_id,
                user_id=user_id,
                message_id=message_id,
                text=text,
                timestamp=message_data.get('date', 0)
            )

            # Handle commands
            if text.startswith('/'):
                await self._handle_command(message)
            else:
                await self._handle_text_message(message)

        except Exception as e:
            logger.error(f"❌ Error handling message: {e}")

    async def _handle_command(self, message: TelegramMessage):
        """Handle command message"""
        try:
            # Parse command and arguments
            parts = message.text.split()
            command = parts[0][1:]  # Remove '/'
            args = parts[1:] if len(parts) > 1 else []

            # Find command handler
            if command in self.commands:
                handler = self.commands[command]

                # Check authorization
                if handler.admin_only and not self._is_authorized(message.user_id):
                    await self.send_message(
                        message.chat_id,
                        "❌ Je bent niet geautoriseerd voor dit commando."
                    )
                    return

                # Execute command
                response = await handler.handle(message, args, self)
                if response:
                    await self.send_message(message.chat_id, response)
            else:
                await self.send_message(
                    message.chat_id,
                    f"❌ Onbekend commando: /{command}\nGebruik /help voor beschikbare commando's."
                )

        except Exception as e:
            logger.error(f"❌ Error handling command: {e}")
            await self.send_message(
                message.chat_id,
                "❌ Er is een fout opgetreden bij het verwerken van het commando."
            )

    async def _handle_text_message(self, message: TelegramMessage):
        """Handle regular text message"""
        # Check if user is in a specific state (waiting for input)
        if message.user_id in self.user_states:
            state = self.user_states[message.user_id]
            # Handle state-specific input
            await self._handle_user_input(message, state)
        else:
            # Handle as regular message or button press
            await self._handle_button_press(message)

    async def _handle_user_input(self, message: TelegramMessage, state: Dict[str, Any]):
        """Handle user input based on current state"""
        # Implementation depends on specific state handling needs
        pass

    async def _handle_button_press(self, message: TelegramMessage):
        """Handle button press from permanent keyboard"""
        # Implementation for permanent keyboard buttons
        pass

    async def handle_callback_query(self, callback_data: Dict):
        """Handle callback query from inline keyboards"""
        try:
            query = CallbackQuery(
                query_id=callback_data['id'],
                chat_id=callback_data['message']['chat']['id'],
                user_id=callback_data['from']['id'],
                message_id=callback_data['message']['message_id'],
                data=callback_data['data']
            )

            # Answer callback immediately
            await self.answer_callback_query(query.query_id, "⚡ Processing...")

            # Find appropriate handler
            for pattern, handler in self.callback_handlers.items():
                if query.data.startswith(pattern):
                    await handler(query, self)
                    return

            # Default handler
            await self.edit_message(
                query.chat_id,
                query.message_id,
                "❌ Onbekende actie. Probeer opnieuw."
            )

        except Exception as e:
            logger.error(f"❌ Error handling callback: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
        logger.info("🧹 Telegram bot handler cleaned up")
