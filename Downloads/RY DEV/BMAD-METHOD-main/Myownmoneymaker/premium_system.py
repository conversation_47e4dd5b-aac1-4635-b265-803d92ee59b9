"""
Premium Subscription System for Trading Bot
Handles subscription tiers, payments, and premium features
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum

class SubscriptionTier(Enum):
    FREE = "free"
    PREMIUM = "premium"  # €50/month
    VIP = "vip"  # €150/month

class PremiumManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
        
        # Subscription prices in EUR
        self.prices = {
            SubscriptionTier.FREE: 0,
            SubscriptionTier.PREMIUM: 50,
            SubscriptionTier.VIP: 150
        }
    
    def init_database(self):
        """Initialize premium subscription tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # User subscriptions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_subscriptions (
                user_id INTEGER PRIMARY KEY,
                tier TEXT DEFAULT 'free',
                start_date TIMESTAMP,
                end_date TIMESTAMP,
                is_active BOOLEAN DEFAULT 0,
                payment_method TEXT,
                last_payment_date TIMESTAMP,
                total_paid REAL DEFAULT 0.0,
                auto_renew BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Payment history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                amount REAL,
                currency TEXT DEFAULT 'EUR',
                tier TEXT,
                payment_method TEXT,
                transaction_id TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Premium features usage tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS premium_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                feature_name TEXT,
                usage_count INTEGER DEFAULT 0,
                last_used TIMESTAMP,
                date DATE,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_user_subscription(self, user_id: int) -> Dict:
        """Get user's current subscription details"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM user_subscriptions WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'user_id': result[0],
                'tier': result[1],
                'start_date': result[2],
                'end_date': result[3],
                'is_active': bool(result[4]),
                'payment_method': result[5],
                'last_payment_date': result[6],
                'total_paid': result[7],
                'auto_renew': bool(result[8]),
                'created_at': result[9]
            }
        else:
            # Create free tier subscription
            return self.create_free_subscription(user_id)
    
    def create_free_subscription(self, user_id: int) -> Dict:
        """Create free tier subscription for new user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_subscriptions (user_id, tier, is_active)
            VALUES (?, ?, ?)
        ''', (user_id, SubscriptionTier.FREE.value, True))
        
        conn.commit()
        conn.close()
        
        return {
            'user_id': user_id,
            'tier': SubscriptionTier.FREE.value,
            'start_date': None,
            'end_date': None,
            'is_active': True,
            'payment_method': None,
            'last_payment_date': None,
            'total_paid': 0.0,
            'auto_renew': True,
            'created_at': datetime.now()
        }
    
    def upgrade_subscription(self, user_id: int, tier: SubscriptionTier, payment_method: str = "manual") -> Dict:
        """Upgrade user to premium tier"""
        if tier == SubscriptionTier.FREE:
            return {"success": False, "error": "Cannot upgrade to free tier"}
        
        start_date = datetime.now()
        end_date = start_date + timedelta(days=30)  # 30 days subscription
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Update subscription
        cursor.execute('''
            UPDATE user_subscriptions 
            SET tier = ?, start_date = ?, end_date = ?, is_active = ?, 
                payment_method = ?, last_payment_date = ?
            WHERE user_id = ?
        ''', (tier.value, start_date, end_date, True, payment_method, start_date, user_id))
        
        # Record payment
        cursor.execute('''
            INSERT INTO payment_history (user_id, amount, tier, payment_method, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, self.prices[tier], tier.value, payment_method, "completed"))
        
        # Update total paid
        cursor.execute('''
            UPDATE user_subscriptions 
            SET total_paid = total_paid + ?
            WHERE user_id = ?
        ''', (self.prices[tier], user_id))
        
        conn.commit()
        conn.close()
        
        return {
            "success": True,
            "tier": tier.value,
            "start_date": start_date,
            "end_date": end_date,
            "amount_paid": self.prices[tier]
        }
    
    def check_subscription_status(self, user_id: int) -> Dict:
        """Check if user's subscription is still active"""
        subscription = self.get_user_subscription(user_id)
        
        if subscription['tier'] == SubscriptionTier.FREE.value:
            return {"is_active": True, "tier": SubscriptionTier.FREE.value, "days_remaining": None}
        
        if subscription['end_date']:
            end_date = datetime.fromisoformat(subscription['end_date'])
            now = datetime.now()
            
            if now > end_date:
                # Subscription expired, downgrade to free
                self.downgrade_to_free(user_id)
                return {"is_active": False, "tier": SubscriptionTier.FREE.value, "days_remaining": 0}
            else:
                days_remaining = (end_date - now).days
                return {"is_active": True, "tier": subscription['tier'], "days_remaining": days_remaining}
        
        return {"is_active": subscription['is_active'], "tier": subscription['tier'], "days_remaining": None}
    
    def downgrade_to_free(self, user_id: int):
        """Downgrade user to free tier"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE user_subscriptions 
            SET tier = ?, is_active = ?, end_date = NULL
            WHERE user_id = ?
        ''', (SubscriptionTier.FREE.value, True, user_id))
        
        conn.commit()
        conn.close()
    
    def has_premium_access(self, user_id: int) -> bool:
        """Check if user has premium access"""
        status = self.check_subscription_status(user_id)
        return status['is_active'] and status['tier'] in [SubscriptionTier.PREMIUM.value, SubscriptionTier.VIP.value]
    
    def has_vip_access(self, user_id: int) -> bool:
        """Check if user has VIP access"""
        status = self.check_subscription_status(user_id)
        return status['is_active'] and status['tier'] == SubscriptionTier.VIP.value
    
    def get_tier_features(self, tier: str) -> Dict:
        """Get features available for each tier"""
        features = {
            SubscriptionTier.FREE.value: {
                "trading_signals_per_week": 3,
                "portfolio_tracking": True,
                "basic_market_data": True,
                "community_chat": True,
                "daily_market_updates": True,
                "basic_trading_bots": False,
                "advanced_analytics": False,
                "priority_support": False,
                "copy_trading": False,
                "custom_alerts": 5,
                "api_calls_per_day": 100
            },
            SubscriptionTier.PREMIUM.value: {
                "trading_signals_per_week": 50,
                "portfolio_tracking": True,
                "basic_market_data": True,
                "community_chat": True,
                "daily_market_updates": True,
                "basic_trading_bots": True,
                "advanced_analytics": True,
                "priority_support": True,
                "copy_trading": True,
                "custom_alerts": 50,
                "api_calls_per_day": 1000,
                "vip_chat_access": True,
                "personal_trading_coach": False,
                "advanced_trading_bots": True,
                "risk_management_tools": True,
                "whale_alerts": True
            },
            SubscriptionTier.VIP.value: {
                "trading_signals_per_week": 999,
                "portfolio_tracking": True,
                "basic_market_data": True,
                "community_chat": True,
                "daily_market_updates": True,
                "basic_trading_bots": True,
                "advanced_analytics": True,
                "priority_support": True,
                "copy_trading": True,
                "custom_alerts": 999,
                "api_calls_per_day": 10000,
                "vip_chat_access": True,
                "personal_trading_coach": True,
                "advanced_trading_bots": True,
                "risk_management_tools": True,
                "whale_alerts": True,
                "custom_bot_development": True,
                "direct_dev_access": True,
                "exclusive_alpha_signals": True
            }
        }
        
        return features.get(tier, features[SubscriptionTier.FREE.value])
    
    def track_feature_usage(self, user_id: int, feature_name: str):
        """Track premium feature usage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        today = datetime.now().date()
        
        # Check if usage record exists for today
        cursor.execute('''
            SELECT usage_count FROM premium_usage 
            WHERE user_id = ? AND feature_name = ? AND date = ?
        ''', (user_id, feature_name, today))
        
        result = cursor.fetchone()
        
        if result:
            # Update existing record
            cursor.execute('''
                UPDATE premium_usage 
                SET usage_count = usage_count + 1, last_used = ?
                WHERE user_id = ? AND feature_name = ? AND date = ?
            ''', (datetime.now(), user_id, feature_name, today))
        else:
            # Create new record
            cursor.execute('''
                INSERT INTO premium_usage (user_id, feature_name, usage_count, last_used, date)
                VALUES (?, ?, 1, ?, ?)
            ''', (user_id, feature_name, datetime.now(), today))
        
        conn.commit()
        conn.close()
    
    def get_usage_stats(self, user_id: int, days: int = 30) -> Dict:
        """Get user's feature usage statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        start_date = datetime.now().date() - timedelta(days=days)
        
        cursor.execute('''
            SELECT feature_name, SUM(usage_count) as total_usage
            FROM premium_usage 
            WHERE user_id = ? AND date >= ?
            GROUP BY feature_name
        ''', (user_id, start_date))
        
        results = cursor.fetchall()
        conn.close()
        
        usage_stats = {}
        for result in results:
            usage_stats[result[0]] = result[1]
        
        return usage_stats
    
    def can_use_feature(self, user_id: int, feature_name: str) -> Tuple[bool, str]:
        """Check if user can use a specific feature"""
        subscription = self.get_user_subscription(user_id)
        status = self.check_subscription_status(user_id)
        
        if not status['is_active']:
            return False, "Subscription expired"
        
        tier_features = self.get_tier_features(status['tier'])
        
        # Check if feature is available for tier
        if feature_name not in tier_features:
            return False, f"Feature not available for {status['tier']} tier"
        
        feature_limit = tier_features[feature_name]
        
        # If feature is boolean, just check if it's enabled
        if isinstance(feature_limit, bool):
            return feature_limit, "Feature available" if feature_limit else "Feature not available for your tier"
        
        # If feature has usage limits, check current usage
        if isinstance(feature_limit, int):
            today = datetime.now().date()
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if "per_week" in feature_name:
                # Weekly limit
                week_start = today - timedelta(days=today.weekday())
                cursor.execute('''
                    SELECT SUM(usage_count) FROM premium_usage 
                    WHERE user_id = ? AND feature_name = ? AND date >= ?
                ''', (user_id, feature_name, week_start))
            else:
                # Daily limit
                cursor.execute('''
                    SELECT usage_count FROM premium_usage 
                    WHERE user_id = ? AND feature_name = ? AND date = ?
                ''', (user_id, feature_name, today))
            
            result = cursor.fetchone()
            conn.close()
            
            current_usage = result[0] if result and result[0] else 0
            
            if current_usage >= feature_limit:
                return False, f"Daily/weekly limit reached ({current_usage}/{feature_limit})"
            
            return True, f"Usage: {current_usage}/{feature_limit}"
        
        return True, "Feature available"
    
    def get_subscription_info_text(self, user_id: int, lang: str = 'nl') -> str:
        """Get formatted subscription information text"""
        subscription = self.get_user_subscription(user_id)
        status = self.check_subscription_status(user_id)
        
        if lang == 'nl':
            if status['tier'] == SubscriptionTier.FREE.value:
                return f"""
🆓 **Gratis Tier**

📊 **Huidige Status:** Actief
🎯 **Functies:**
• 3 trading signalen per week
• Basis marktdata
• Community chat toegang
• Portfolio tracking
• 5 aangepaste alerts

💎 **Upgrade naar Premium (€50/maand):**
• 50 trading signalen per week
• Geavanceerde analytics
• Trading bots
• Priority support
• VIP chat toegang

👑 **Upgrade naar VIP (€150/maand):**
• Onbeperkte signalen
• Persoonlijke trading coach
• Custom bot development
• Exclusieve alpha signalen
"""
            else:
                days_remaining = status['days_remaining']
                return f"""
{'💎' if status['tier'] == 'premium' else '👑'} **{status['tier'].title()} Tier**

📊 **Status:** {'Actief' if status['is_active'] else 'Verlopen'}
⏰ **Dagen Resterend:** {days_remaining if days_remaining else 'Onbeperkt'}
💰 **Totaal Betaald:** €{subscription['total_paid']:.2f}
🔄 **Auto Verlenging:** {'Aan' if subscription['auto_renew'] else 'Uit'}

✨ **Je Premium Voordelen zijn actief!**
"""
        else:
            # English version
            if status['tier'] == SubscriptionTier.FREE.value:
                return f"""
🆓 **Free Tier**

📊 **Current Status:** Active
🎯 **Features:**
• 3 trading signals per week
• Basic market data
• Community chat access
• Portfolio tracking
• 5 custom alerts

💎 **Upgrade to Premium (€50/month):**
• 50 trading signals per week
• Advanced analytics
• Trading bots
• Priority support
• VIP chat access

👑 **Upgrade to VIP (€150/month):**
• Unlimited signals
• Personal trading coach
• Custom bot development
• Exclusive alpha signals
"""
            else:
                days_remaining = status['days_remaining']
                return f"""
{'💎' if status['tier'] == 'premium' else '👑'} **{status['tier'].title()} Tier**

📊 **Status:** {'Active' if status['is_active'] else 'Expired'}
⏰ **Days Remaining:** {days_remaining if days_remaining else 'Unlimited'}
💰 **Total Paid:** €{subscription['total_paid']:.2f}
🔄 **Auto Renewal:** {'On' if subscription['auto_renew'] else 'Off'}

✨ **Your Premium Benefits are active!**
"""
