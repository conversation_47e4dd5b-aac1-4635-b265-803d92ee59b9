"""
Real Trading System for Live Trading with Real Money
ONLY for authorized admin accounts for testing purposes
"""

import sqlite3
import json
import ccxt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import os
from decimal import Decimal

class RealTradingManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self.init_database()
        
        # Authorized admin IDs for real trading (ONLY YOUR IDs)
        self.authorized_real_traders = [**********, **********]  # Your admin IDs
        
        # Initialize real exchanges (will be configured later)
        self.real_exchanges = {}
        
        # Trading limits for safety
        self.max_trade_amount_eur = 100  # Maximum €100 per trade for testing
        self.daily_trade_limit = 500    # Maximum €500 per day
        
    def init_database(self):
        """Initialize real trading tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Real trading accounts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS real_trading_accounts (
                user_id INTEGER PRIMARY KEY,
                exchange_name TEXT,
                api_key_encrypted TEXT,
                api_secret_encrypted TEXT,
                is_active BOOLEAN DEFAULT 0,
                is_testnet BOOLEAN DEFAULT 1,
                balance_eur REAL DEFAULT 0.0,
                total_trades INTEGER DEFAULT 0,
                total_profit REAL DEFAULT 0.0,
                daily_volume REAL DEFAULT 0.0,
                last_trade_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Real trade history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS real_trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                exchange TEXT,
                symbol TEXT,
                side TEXT, -- 'buy' or 'sell'
                amount REAL,
                price REAL,
                cost REAL,
                fee REAL,
                order_id TEXT,
                status TEXT, -- 'pending', 'filled', 'cancelled', 'failed'
                profit_loss REAL DEFAULT 0.0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                order_type TEXT DEFAULT 'market',
                is_real_money BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Trading performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                date DATE,
                total_trades INTEGER DEFAULT 0,
                successful_trades INTEGER DEFAULT 0,
                total_volume REAL DEFAULT 0.0,
                total_profit REAL DEFAULT 0.0,
                best_trade REAL DEFAULT 0.0,
                worst_trade REAL DEFAULT 0.0,
                win_rate REAL DEFAULT 0.0,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        # Risk management settings
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS risk_settings (
                user_id INTEGER PRIMARY KEY,
                max_trade_amount REAL DEFAULT 50.0,
                max_daily_volume REAL DEFAULT 200.0,
                stop_loss_percentage REAL DEFAULT 5.0,
                take_profit_percentage REAL DEFAULT 10.0,
                max_open_positions INTEGER DEFAULT 3,
                risk_level TEXT DEFAULT 'conservative',
                auto_stop_loss BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (telegram_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def is_authorized_for_real_trading(self, user_id: int) -> bool:
        """Check if user is authorized for real money trading"""
        return user_id in self.authorized_real_traders
    
    def setup_real_exchange(self, user_id: int, exchange_name: str, api_key: str, api_secret: str, testnet: bool = True) -> Dict:
        """Setup real exchange connection for authorized user"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized for real trading"}
        
        try:
            # Initialize exchange
            if exchange_name.lower() == 'kucoin':
                exchange = ccxt.kucoin({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'sandbox': testnet,  # Use testnet for safety
                    'enableRateLimit': True,
                })
            elif exchange_name.lower() == 'mexc':
                exchange = ccxt.mexc({
                    'apiKey': api_key,
                    'secret': api_secret,
                    'sandbox': testnet,
                    'enableRateLimit': True,
                })
            else:
                return {"success": False, "error": f"Exchange {exchange_name} not supported"}
            
            # Test connection
            balance = exchange.fetch_balance()
            
            # Store encrypted credentials (simplified - in production use proper encryption)
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO real_trading_accounts 
                (user_id, exchange_name, api_key_encrypted, api_secret_encrypted, is_active, is_testnet)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, exchange_name, api_key[:10] + "...", "***", True, testnet))
            
            conn.commit()
            conn.close()
            
            # Store exchange instance
            self.real_exchanges[user_id] = exchange
            
            return {
                "success": True,
                "exchange": exchange_name,
                "testnet": testnet,
                "balance": balance.get('USDT', {}).get('free', 0)
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to setup exchange: {str(e)}"}
    
    def get_real_balance(self, user_id: int) -> Dict:
        """Get real account balance"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized"}
        
        if user_id not in self.real_exchanges:
            return {"success": False, "error": "Exchange not configured"}
        
        try:
            exchange = self.real_exchanges[user_id]
            balance = exchange.fetch_balance()
            
            return {
                "success": True,
                "balance": balance,
                "usdt_balance": balance.get('USDT', {}).get('free', 0),
                "total_value": sum([coin.get('free', 0) * self.get_coin_price_in_usdt(exchange, symbol) 
                                  for symbol, coin in balance.items() if coin.get('free', 0) > 0])
            }
            
        except Exception as e:
            return {"success": False, "error": f"Failed to get balance: {str(e)}"}
    
    def get_coin_price_in_usdt(self, exchange, symbol: str) -> float:
        """Get coin price in USDT"""
        try:
            if symbol == 'USDT':
                return 1.0
            
            ticker = exchange.fetch_ticker(f"{symbol}/USDT")
            return ticker['last']
        except:
            return 0.0
    
    def place_real_order(self, user_id: int, symbol: str, side: str, amount: float, order_type: str = 'market') -> Dict:
        """Place real money order (ONLY for authorized users)"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized for real trading"}
        
        if user_id not in self.real_exchanges:
            return {"success": False, "error": "Exchange not configured"}
        
        # Safety checks
        if amount * self.get_current_price(symbol) > self.max_trade_amount_eur:
            return {"success": False, "error": f"Trade amount exceeds maximum (€{self.max_trade_amount_eur})"}
        
        if not self.check_daily_limit(user_id, amount):
            return {"success": False, "error": f"Daily trading limit exceeded (€{self.daily_trade_limit})"}
        
        try:
            exchange = self.real_exchanges[user_id]
            
            # Place order
            if order_type == 'market':
                if side == 'buy':
                    order = exchange.create_market_buy_order(symbol, amount)
                else:
                    order = exchange.create_market_sell_order(symbol, amount)
            else:
                return {"success": False, "error": "Only market orders supported for now"}
            
            # Record trade
            self.record_real_trade(user_id, order, symbol, side, amount)
            
            return {
                "success": True,
                "order": order,
                "order_id": order['id'],
                "status": order['status'],
                "filled": order.get('filled', 0),
                "cost": order.get('cost', 0)
            }
            
        except Exception as e:
            return {"success": False, "error": f"Order failed: {str(e)}"}
    
    def record_real_trade(self, user_id: int, order: Dict, symbol: str, side: str, amount: float):
        """Record real trade in database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO real_trades 
            (user_id, exchange, symbol, side, amount, price, cost, fee, order_id, status, order_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id,
            order.get('info', {}).get('exchange', 'unknown'),
            symbol,
            side,
            amount,
            order.get('price', 0),
            order.get('cost', 0),
            order.get('fee', {}).get('cost', 0),
            order.get('id', ''),
            order.get('status', 'unknown'),
            'market'
        ))
        
        conn.commit()
        conn.close()
    
    def get_current_price(self, symbol: str) -> float:
        """Get current price for safety calculations"""
        # This should connect to your existing price fetching system
        # For now, return a default value
        return 50000.0  # Default BTC price for calculations
    
    def check_daily_limit(self, user_id: int, trade_amount_eur: float) -> bool:
        """Check if user hasn't exceeded daily trading limit"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        today = datetime.now().date()
        
        cursor.execute('''
            SELECT SUM(cost) FROM real_trades 
            WHERE user_id = ? AND DATE(timestamp) = ? AND status = 'filled'
        ''', (user_id, today))
        
        result = cursor.fetchone()
        conn.close()
        
        daily_volume = result[0] if result[0] else 0
        
        return (daily_volume + trade_amount_eur) <= self.daily_trade_limit
    
    def get_trading_performance(self, user_id: int, days: int = 30) -> Dict:
        """Get user's real trading performance"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized"}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        start_date = datetime.now().date() - timedelta(days=days)
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total_trades,
                SUM(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as winning_trades,
                SUM(cost) as total_volume,
                SUM(profit_loss) as total_profit,
                MAX(profit_loss) as best_trade,
                MIN(profit_loss) as worst_trade
            FROM real_trades 
            WHERE user_id = ? AND DATE(timestamp) >= ? AND status = 'filled'
        ''', (user_id, start_date))
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0] > 0:
            total_trades = result[0]
            winning_trades = result[1]
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            return {
                "success": True,
                "period_days": days,
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": total_trades - winning_trades,
                "win_rate": win_rate,
                "total_volume": result[2] or 0,
                "total_profit": result[3] or 0,
                "best_trade": result[4] or 0,
                "worst_trade": result[5] or 0,
                "average_profit_per_trade": (result[3] / total_trades) if total_trades > 0 else 0
            }
        else:
            return {
                "success": True,
                "period_days": days,
                "total_trades": 0,
                "message": "No trades found in this period"
            }
    
    def get_risk_settings(self, user_id: int) -> Dict:
        """Get user's risk management settings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM risk_settings WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()
        
        if result:
            return {
                "max_trade_amount": result[1],
                "max_daily_volume": result[2],
                "stop_loss_percentage": result[3],
                "take_profit_percentage": result[4],
                "max_open_positions": result[5],
                "risk_level": result[6],
                "auto_stop_loss": bool(result[7])
            }
        else:
            # Create default settings
            cursor.execute('''
                INSERT INTO risk_settings (user_id) VALUES (?)
            ''', (user_id,))
            conn.commit()
            conn.close()
            
            return {
                "max_trade_amount": 50.0,
                "max_daily_volume": 200.0,
                "stop_loss_percentage": 5.0,
                "take_profit_percentage": 10.0,
                "max_open_positions": 3,
                "risk_level": "conservative",
                "auto_stop_loss": True
            }
    
    def update_risk_settings(self, user_id: int, settings: Dict) -> Dict:
        """Update user's risk management settings"""
        if not self.is_authorized_for_real_trading(user_id):
            return {"success": False, "error": "Not authorized"}
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE risk_settings SET
                max_trade_amount = ?,
                max_daily_volume = ?,
                stop_loss_percentage = ?,
                take_profit_percentage = ?,
                max_open_positions = ?,
                risk_level = ?,
                auto_stop_loss = ?
            WHERE user_id = ?
        ''', (
            settings.get('max_trade_amount', 50.0),
            settings.get('max_daily_volume', 200.0),
            settings.get('stop_loss_percentage', 5.0),
            settings.get('take_profit_percentage', 10.0),
            settings.get('max_open_positions', 3),
            settings.get('risk_level', 'conservative'),
            settings.get('auto_stop_loss', True),
            user_id
        ))
        
        conn.commit()
        conn.close()
        
        return {"success": True, "message": "Risk settings updated"}
    
    def get_real_trading_status_text(self, user_id: int, lang: str = 'nl') -> str:
        """Get formatted real trading status text"""
        if not self.is_authorized_for_real_trading(user_id):
            if lang == 'nl':
                return "❌ **Niet geautoriseerd voor real trading**\n\nAlleen admins kunnen real money trading gebruiken."
            else:
                return "❌ **Not authorized for real trading**\n\nOnly admins can use real money trading."
        
        balance = self.get_real_balance(user_id)
        performance = self.get_trading_performance(user_id, 7)  # Last 7 days
        risk_settings = self.get_risk_settings(user_id)
        
        if lang == 'nl':
            text = f"""
🔴 **REAL MONEY TRADING** 🔴

💰 **Account Status:**
"""
            if balance['success']:
                text += f"• USDT Saldo: ${balance['usdt_balance']:.2f}\n"
                text += f"• Totale Waarde: ${balance.get('total_value', 0):.2f}\n"
            else:
                text += "• Status: Niet verbonden\n"
            
            text += f"""
📊 **Performance (7 dagen):**
"""
            if performance['success'] and performance['total_trades'] > 0:
                text += f"• Trades: {performance['total_trades']}\n"
                text += f"• Win Rate: {performance['win_rate']:.1f}%\n"
                text += f"• Totale Winst: €{performance['total_profit']:.2f}\n"
                text += f"• Beste Trade: €{performance['best_trade']:.2f}\n"
            else:
                text += "• Geen trades deze week\n"
            
            text += f"""
🛡️ **Risk Management:**
• Max Trade: €{risk_settings['max_trade_amount']:.0f}
• Dagelijkse Limiet: €{risk_settings['max_daily_volume']:.0f}
• Stop Loss: {risk_settings['stop_loss_percentage']:.1f}%
• Take Profit: {risk_settings['take_profit_percentage']:.1f}%

⚠️ **WAARSCHUWING:** Dit is real money trading!
"""
        else:
            text = f"""
🔴 **REAL MONEY TRADING** 🔴

💰 **Account Status:**
"""
            if balance['success']:
                text += f"• USDT Balance: ${balance['usdt_balance']:.2f}\n"
                text += f"• Total Value: ${balance.get('total_value', 0):.2f}\n"
            else:
                text += "• Status: Not connected\n"
            
            text += f"""
📊 **Performance (7 days):**
"""
            if performance['success'] and performance['total_trades'] > 0:
                text += f"• Trades: {performance['total_trades']}\n"
                text += f"• Win Rate: {performance['win_rate']:.1f}%\n"
                text += f"• Total Profit: €{performance['total_profit']:.2f}\n"
                text += f"• Best Trade: €{performance['best_trade']:.2f}\n"
            else:
                text += "• No trades this week\n"
            
            text += f"""
🛡️ **Risk Management:**
• Max Trade: €{risk_settings['max_trade_amount']:.0f}
• Daily Limit: €{risk_settings['max_daily_volume']:.0f}
• Stop Loss: {risk_settings['stop_loss_percentage']:.1f}%
• Take Profit: {risk_settings['take_profit_percentage']:.1f}%

⚠️ **WARNING:** This is real money trading!
"""
        
        return text
