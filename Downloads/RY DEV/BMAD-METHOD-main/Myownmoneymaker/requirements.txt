# Telegram Bot
python-telegram-bot>=20.0,<21.0

# Exchange APIs
ccxt==4.2.25

# Environment variables
python-dotenv>=1.0.0

# Async support
aiohttp==3.9.1

# Logging
loguru==0.7.2

# Data Analysis & Technical Indicators
pandas>=2.0.0
pandas_ta>=0.3.14b

# Configuration (disabled for Python 3.13 compatibility)
# pydantic==2.5.2
# pydantic-settings==2.1.0

# Security
cryptography>=41.0.0

# Testing (optional)
# pytest==7.4.3
# pytest-asyncio==0.21.1

# Additional Dependencies
tweepy>=4.14.0
praw>=7.7.1
newsapi-python>=0.2.7
# Core Bot Dependencies
pyTelegramBotAPI==4.14.0
python-dotenv==1.0.0

# Exchange APIs
ccxt==4.1.64

# Database
sqlite3

# Async Support
asyncio
aiohttp==3.9.1

# Data Processing
pandas==2.1.4
numpy==1.25.2

# Date/Time
python-dateutil==2.8.2

# Logging
colorlog==6.8.0

# HTTP Requests
requests==2.31.0

# JSON Processing
ujson==5.9.0

# Cryptography (for API signatures)
cryptography==41.0.8

# Environment Management
python-decouple==3.8

# Monitoring (Optional)
psutil==5.9.6

# Testing (Development)
pytest==7.4.3
pytest-asyncio==0.21.1

# Code Quality (Development)
black==23.11.0
flake8==6.1.0

# Type Hints (Development)
mypy==1.7.1
