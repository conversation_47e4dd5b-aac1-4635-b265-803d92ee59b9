#!/bin/bash
# Trading Bot Deployment Script
# Generated on 2025-05-29 04:03:12

echo "🚀 Starting Trading Bot Deployment..."

# Create necessary directories
mkdir -p logs data backups

# Set file permissions
chmod 600 .env
chmod 755 *.py
chmod 755 start_heartbeat.sh

# Install dependencies
echo "📦 Installing dependencies..."
pip install -r requirements.txt

# Run production readiness check
echo "🔍 Running production readiness check..."
python3 -c "
from core.production_manager import ProductionManager
pm = ProductionManager()
result = pm.check_production_readiness()
print(f'Production Ready: {result["production_ready"]}')
print(f'Score: {result["overall_score"]}%')
"

# Start services
echo "🚀 Starting services..."

# Start heartbeat monitor in background
nohup python3 heartbeat_monitor.py > logs/heartbeat.log 2>&1 &
echo "💓 Heartbeat monitor started"

# Start main bot
echo "🤖 Starting main bot..."
python3 telegram_simple.py

echo "✅ Deployment complete!"
