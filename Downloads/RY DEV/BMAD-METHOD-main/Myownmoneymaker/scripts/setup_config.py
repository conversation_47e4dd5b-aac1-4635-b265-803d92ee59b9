#!/usr/bin/env python3
"""
Configuration Setup Script voor Trading Bot
Interactieve setup voor het configureren van de trading bot
"""

import os
import sys
import json
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import getpass

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config.config_validator import validate_config, ValidationResult


class ConfigSetup:
    """Interactive configuration setup for trading bot"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.env_file = self.project_root / ".env"
        self.env_example = self.project_root / ".env.example"
        
        self.config_data = {}
        
    def run_setup(self):
        """Run the complete setup process"""
        print("🤖 Trading Bot Configuration Setup")
        print("=" * 50)
        
        # Check if .env already exists
        if self.env_file.exists():
            overwrite = input("⚠️ .env file already exists. Overwrite? (y/N): ").lower()
            if overwrite != 'y':
                print("Setup cancelled.")
                return
        
        # Run setup steps
        self._setup_environment()
        self._setup_telegram()
        self._setup_exchanges()
        self._setup_trading()
        self._setup_security()
        self._setup_database()
        self._setup_monitoring()
        
        # Generate .env file
        self._generate_env_file()
        
        # Validate configuration
        self._validate_configuration()
        
        # Setup complete
        self._setup_complete()
    
    def _setup_environment(self):
        """Setup environment configuration"""
        print("\n📋 Environment Configuration")
        print("-" * 30)
        
        environments = ["development", "staging", "production"]
        print("Available environments:")
        for i, env in enumerate(environments, 1):
            print(f"  {i}. {env}")
        
        while True:
            try:
                choice = int(input("Select environment (1-3): "))
                if 1 <= choice <= 3:
                    self.config_data["ENVIRONMENT"] = environments[choice - 1]
                    break
                else:
                    print("Please enter a number between 1 and 3")
            except ValueError:
                print("Please enter a valid number")
        
        # Debug mode
        if self.config_data["ENVIRONMENT"] == "development":
            self.config_data["DEBUG_MODE"] = "true"
            self.config_data["TEST_MODE"] = "true"
            self.config_data["LIVE_MODE"] = "false"
        else:
            debug = input("Enable debug mode? (y/N): ").lower() == 'y'
            self.config_data["DEBUG_MODE"] = "true" if debug else "false"
            
            test_mode = input("Enable test mode? (Y/n): ").lower() != 'n'
            self.config_data["TEST_MODE"] = "true" if test_mode else "false"
            self.config_data["LIVE_MODE"] = "false" if test_mode else "true"
    
    def _setup_telegram(self):
        """Setup Telegram bot configuration"""
        print("\n📱 Telegram Bot Configuration")
        print("-" * 30)
        
        print("You need to create a Telegram bot first:")
        print("1. Message @BotFather on Telegram")
        print("2. Send /newbot command")
        print("3. Follow the instructions")
        print("4. Copy the bot token")
        
        while True:
            bot_token = input("\nEnter your Telegram bot token: ").strip()
            if bot_token and len(bot_token) > 40:
                self.config_data["TELEGRAM_BOT_TOKEN"] = bot_token
                break
            else:
                print("❌ Invalid bot token. Please try again.")
        
        print("\nTo get your Telegram user ID:")
        print("1. Message @userinfobot on Telegram")
        print("2. Copy your user ID")
        
        while True:
            try:
                user_id = int(input("Enter your Telegram user ID: "))
                if user_id > 0:
                    self.config_data["TELEGRAM_ADMIN_USER_ID"] = str(user_id)
                    break
                else:
                    print("❌ User ID must be positive")
            except ValueError:
                print("❌ Please enter a valid number")
    
    def _setup_exchanges(self):
        """Setup exchange configurations"""
        print("\n💱 Exchange Configuration")
        print("-" * 30)
        
        exchanges = ["kucoin", "mexc", "binance"]
        print("Available exchanges:")
        for i, exchange in enumerate(exchanges, 1):
            print(f"  {i}. {exchange.upper()}")
        
        while True:
            try:
                choice = int(input("Select default exchange (1-3): "))
                if 1 <= choice <= 3:
                    self.config_data["EXCHANGE"] = exchanges[choice - 1]
                    break
                else:
                    print("Please enter a number between 1 and 3")
            except ValueError:
                print("Please enter a valid number")
        
        # Setup selected exchange
        if self.config_data["EXCHANGE"] == "kucoin":
            self._setup_kucoin()
        elif self.config_data["EXCHANGE"] == "mexc":
            self._setup_mexc()
        elif self.config_data["EXCHANGE"] == "binance":
            self._setup_binance()
    
    def _setup_kucoin(self):
        """Setup KuCoin exchange"""
        print("\n🔑 KuCoin API Configuration")
        print("Get your API keys from: https://www.kucoin.com/account/api")
        
        api_key = input("KuCoin API Key: ").strip()
        secret_key = getpass.getpass("KuCoin Secret Key: ").strip()
        passphrase = getpass.getpass("KuCoin Passphrase: ").strip()
        
        self.config_data["KUCOIN_API_KEY"] = api_key
        self.config_data["KUCOIN_SECRET_KEY"] = secret_key
        self.config_data["KUCOIN_PASSPHRASE"] = passphrase
        
        # Sandbox mode
        if self.config_data.get("TEST_MODE") == "true":
            self.config_data["KUCOIN_SANDBOX"] = "true"
        else:
            sandbox = input("Use KuCoin sandbox (recommended for testing)? (Y/n): ").lower() != 'n'
            self.config_data["KUCOIN_SANDBOX"] = "true" if sandbox else "false"
    
    def _setup_mexc(self):
        """Setup MEXC exchange"""
        print("\n🔑 MEXC API Configuration")
        print("Get your API keys from: https://www.mexc.com/user/api")
        
        api_key = input("MEXC API Key: ").strip()
        secret_key = getpass.getpass("MEXC Secret Key: ").strip()
        
        self.config_data["MEXC_API_KEY"] = api_key
        self.config_data["MEXC_SECRET_KEY"] = secret_key
        
        # Sandbox mode
        if self.config_data.get("TEST_MODE") == "true":
            self.config_data["MEXC_SANDBOX"] = "true"
        else:
            sandbox = input("Use MEXC sandbox (recommended for testing)? (Y/n): ").lower() != 'n'
            self.config_data["MEXC_SANDBOX"] = "true" if sandbox else "false"
    
    def _setup_binance(self):
        """Setup Binance exchange"""
        print("\n🔑 Binance API Configuration")
        print("Get your API keys from: https://www.binance.com/en/my/settings/api-management")
        
        api_key = input("Binance API Key: ").strip()
        secret_key = getpass.getpass("Binance Secret Key: ").strip()
        
        self.config_data["BINANCE_API_KEY"] = api_key
        self.config_data["BINANCE_SECRET_KEY"] = secret_key
        
        # Testnet mode
        if self.config_data.get("TEST_MODE") == "true":
            self.config_data["BINANCE_TESTNET"] = "true"
        else:
            testnet = input("Use Binance testnet (recommended for testing)? (Y/n): ").lower() != 'n'
            self.config_data["BINANCE_TESTNET"] = "true" if testnet else "false"
    
    def _setup_trading(self):
        """Setup trading configuration"""
        print("\n📈 Trading Configuration")
        print("-" * 30)
        
        # Trading limits
        while True:
            try:
                daily_limit = float(input("Daily trading limit (USD) [100.0]: ") or "100.0")
                if daily_limit > 0:
                    self.config_data["DEFAULT_DAILY_LIMIT"] = str(daily_limit)
                    break
                else:
                    print("❌ Daily limit must be positive")
            except ValueError:
                print("❌ Please enter a valid number")
        
        while True:
            try:
                max_trades = int(input("Maximum trades per day [10]: ") or "10")
                if max_trades > 0:
                    self.config_data["MAX_TRADES_PER_DAY"] = str(max_trades)
                    break
                else:
                    print("❌ Max trades must be positive")
            except ValueError:
                print("❌ Please enter a valid number")
        
        # Risk management
        while True:
            try:
                risk_per_trade = float(input("Risk per trade (0.01 = 1%) [0.02]: ") or "0.02")
                if 0 < risk_per_trade <= 1:
                    self.config_data["RISK_PER_TRADE"] = str(risk_per_trade)
                    break
                else:
                    print("❌ Risk per trade must be between 0 and 1")
            except ValueError:
                print("❌ Please enter a valid number")
    
    def _setup_security(self):
        """Setup security configuration"""
        print("\n🔒 Security Configuration")
        print("-" * 30)
        
        # Generate encryption key
        import secrets
        encryption_key = secrets.token_urlsafe(32)
        self.config_data["ENCRYPTION_KEY"] = encryption_key
        print("✅ Generated encryption key")
        
        # Rate limiting
        rate_limit = input("Enable rate limiting? (Y/n): ").lower() != 'n'
        self.config_data["RATE_LIMIT_ENABLED"] = "true" if rate_limit else "false"
        
        # 2FA
        enable_2fa = input("Enable 2FA (future feature)? (y/N): ").lower() == 'y'
        self.config_data["ENABLE_2FA"] = "true" if enable_2fa else "false"
    
    def _setup_database(self):
        """Setup database configuration"""
        print("\n🗄️ Database Configuration")
        print("-" * 30)
        
        db_types = ["sqlite", "postgresql", "mysql"]
        print("Available database types:")
        for i, db_type in enumerate(db_types, 1):
            print(f"  {i}. {db_type}")
        
        while True:
            try:
                choice = int(input("Select database type (1-3) [1]: ") or "1")
                if 1 <= choice <= 3:
                    if choice == 1:  # SQLite
                        db_name = input("Database name [trading_bot.db]: ") or "trading_bot.db"
                        self.config_data["DATABASE_URL"] = f"sqlite:///{db_name}"
                    else:
                        print("PostgreSQL and MySQL setup not implemented yet. Using SQLite.")
                        self.config_data["DATABASE_URL"] = "sqlite:///trading_bot.db"
                    break
                else:
                    print("Please enter a number between 1 and 3")
            except ValueError:
                print("Please enter a valid number")
        
        # Backup settings
        backup = input("Enable database backups? (Y/n): ").lower() != 'n'
        self.config_data["BACKUP_ENABLED"] = "true" if backup else "false"
    
    def _setup_monitoring(self):
        """Setup monitoring configuration"""
        print("\n📊 Monitoring Configuration")
        print("-" * 30)
        
        # Health checks
        health_check = input("Enable health checks? (Y/n): ").lower() != 'n'
        self.config_data["HEALTH_CHECK_ENABLED"] = "true" if health_check else "false"
        
        # Notifications
        notifications = input("Enable notifications? (Y/n): ").lower() != 'n'
        self.config_data["ENABLE_NOTIFICATIONS"] = "true" if notifications else "false"
        
        # Performance monitoring
        performance = input("Enable performance monitoring? (Y/n): ").lower() != 'n'
        self.config_data["PERFORMANCE_MONITORING"] = "true" if performance else "false"
    
    def _generate_env_file(self):
        """Generate .env file from configuration"""
        print("\n📝 Generating .env file...")
        
        # Copy from example if it exists
        if self.env_example.exists():
            shutil.copy(self.env_example, self.env_file)
        
        # Update with user configuration
        env_content = []
        
        # Add header
        env_content.append("# Trading Bot Configuration")
        env_content.append("# Generated by setup script")
        env_content.append("")
        
        # Add configuration values
        for key, value in self.config_data.items():
            env_content.append(f"{key}={value}")
        
        # Write to file
        with open(self.env_file, 'w') as f:
            f.write('\n'.join(env_content))
        
        print(f"✅ Configuration saved to {self.env_file}")
    
    def _validate_configuration(self):
        """Validate the generated configuration"""
        print("\n🔍 Validating configuration...")
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv(self.env_file)
        
        # Run validation
        result = validate_config()
        
        if result.is_valid:
            print("✅ Configuration is valid!")
        else:
            print("❌ Configuration has errors:")
            for error in result.errors:
                print(f"  • {error}")
        
        if result.warnings:
            print("⚠️ Warnings:")
            for warning in result.warnings:
                print(f"  • {warning}")
        
        if result.suggestions:
            print("💡 Suggestions:")
            for suggestion in result.suggestions:
                print(f"  • {suggestion}")
    
    def _setup_complete(self):
        """Setup completion message"""
        print("\n🎉 Setup Complete!")
        print("=" * 50)
        print("Your trading bot is now configured!")
        print("")
        print("Next steps:")
        print("1. Review your .env file")
        print("2. Test your configuration: python scripts/test_config.py")
        print("3. Start the bot: python simple_bot.py")
        print("")
        print("⚠️ Important:")
        print("- Keep your .env file secure")
        print("- Never commit .env to version control")
        print("- Start with test mode enabled")
        print("")
        print("📚 Documentation: docs/")
        print("🆘 Support: Check README.md")


def main():
    """Main setup function"""
    try:
        setup = ConfigSetup()
        setup.run_setup()
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
