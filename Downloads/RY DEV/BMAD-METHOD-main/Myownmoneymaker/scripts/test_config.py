#!/usr/bin/env python3
"""
Configuration Test Script voor Trading Bot
Test alle configuratie-aspecten en connecties
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import Dict, Any, List
import json

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from config.config_validator import validate_config
from config.config_manager import get_config_manager


class ConfigTester:
    """Comprehensive configuration tester"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.results = {
            "environment": {"status": "unknown", "details": []},
            "telegram": {"status": "unknown", "details": []},
            "exchanges": {"status": "unknown", "details": []},
            "database": {"status": "unknown", "details": []},
            "security": {"status": "unknown", "details": []},
            "overall": {"status": "unknown", "score": 0}
        }
        
        # Load environment variables
        load_dotenv()
    
    async def run_all_tests(self):
        """Run all configuration tests"""
        print("🧪 Trading Bot Configuration Tests")
        print("=" * 50)
        
        # Run tests
        await self._test_environment()
        await self._test_telegram()
        await self._test_exchanges()
        await self._test_database()
        await self._test_security()
        
        # Calculate overall score
        self._calculate_overall_score()
        
        # Display results
        self._display_results()
        
        return self.results
    
    async def _test_environment(self):
        """Test environment configuration"""
        print("\n🌍 Testing Environment Configuration...")
        
        try:
            # Test environment variables loading
            env_file = self.project_root / ".env"
            if not env_file.exists():
                self.results["environment"]["status"] = "error"
                self.results["environment"]["details"].append("❌ .env file not found")
                return
            
            # Test config manager
            try:
                config_manager = get_config_manager()
                self.results["environment"]["details"].append("✅ Config manager initialized")
            except Exception as e:
                self.results["environment"]["details"].append(f"❌ Config manager failed: {e}")
                self.results["environment"]["status"] = "error"
                return
            
            # Test environment detection
            environment = os.getenv("ENVIRONMENT", "development")
            self.results["environment"]["details"].append(f"✅ Environment: {environment}")
            
            # Test debug mode
            debug_mode = os.getenv("DEBUG_MODE", "false").lower() == "true"
            self.results["environment"]["details"].append(f"ℹ️ Debug mode: {debug_mode}")
            
            # Test mode consistency
            test_mode = os.getenv("TEST_MODE", "true").lower() == "true"
            live_mode = os.getenv("LIVE_MODE", "false").lower() == "true"
            
            if test_mode and live_mode:
                self.results["environment"]["details"].append("❌ Both TEST_MODE and LIVE_MODE are enabled")
                self.results["environment"]["status"] = "error"
            elif not test_mode and not live_mode:
                self.results["environment"]["details"].append("⚠️ Neither TEST_MODE nor LIVE_MODE is enabled")
                self.results["environment"]["status"] = "warning"
            else:
                mode = "test" if test_mode else "live"
                self.results["environment"]["details"].append(f"✅ Trading mode: {mode}")
                self.results["environment"]["status"] = "success"
            
        except Exception as e:
            self.results["environment"]["status"] = "error"
            self.results["environment"]["details"].append(f"❌ Environment test failed: {e}")
    
    async def _test_telegram(self):
        """Test Telegram bot configuration"""
        print("📱 Testing Telegram Configuration...")
        
        try:
            bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
            admin_user_id = os.getenv("TELEGRAM_ADMIN_USER_ID")
            
            if not bot_token:
                self.results["telegram"]["status"] = "error"
                self.results["telegram"]["details"].append("❌ TELEGRAM_BOT_TOKEN not set")
                return
            
            if not admin_user_id:
                self.results["telegram"]["status"] = "error"
                self.results["telegram"]["details"].append("❌ TELEGRAM_ADMIN_USER_ID not set")
                return
            
            # Test bot token format
            if len(bot_token) < 40:
                self.results["telegram"]["details"].append("⚠️ Bot token seems too short")
                self.results["telegram"]["status"] = "warning"
            else:
                self.results["telegram"]["details"].append("✅ Bot token format looks valid")
            
            # Test admin user ID
            try:
                admin_id = int(admin_user_id)
                if admin_id > 0:
                    self.results["telegram"]["details"].append(f"✅ Admin user ID: {admin_id}")
                else:
                    self.results["telegram"]["details"].append("❌ Admin user ID must be positive")
                    self.results["telegram"]["status"] = "error"
                    return
            except ValueError:
                self.results["telegram"]["details"].append("❌ Admin user ID must be a number")
                self.results["telegram"]["status"] = "error"
                return
            
            # Test bot connection (if possible)
            try:
                import telebot
                bot = telebot.TeleBot(bot_token)
                bot_info = bot.get_me()
                self.results["telegram"]["details"].append(f"✅ Bot connected: @{bot_info.username}")
                self.results["telegram"]["status"] = "success"
            except Exception as e:
                self.results["telegram"]["details"].append(f"❌ Bot connection failed: {e}")
                self.results["telegram"]["status"] = "error"
            
        except Exception as e:
            self.results["telegram"]["status"] = "error"
            self.results["telegram"]["details"].append(f"❌ Telegram test failed: {e}")
    
    async def _test_exchanges(self):
        """Test exchange configurations"""
        print("💱 Testing Exchange Configurations...")
        
        try:
            default_exchange = os.getenv("EXCHANGE", "kucoin").lower()
            self.results["exchanges"]["details"].append(f"ℹ️ Default exchange: {default_exchange}")
            
            exchange_tested = False
            
            # Test KuCoin
            if await self._test_kucoin():
                exchange_tested = True
            
            # Test MEXC
            if await self._test_mexc():
                exchange_tested = True
            
            # Test Binance
            if await self._test_binance():
                exchange_tested = True
            
            if exchange_tested:
                self.results["exchanges"]["status"] = "success"
            else:
                self.results["exchanges"]["status"] = "error"
                self.results["exchanges"]["details"].append("❌ No exchanges properly configured")
            
        except Exception as e:
            self.results["exchanges"]["status"] = "error"
            self.results["exchanges"]["details"].append(f"❌ Exchange test failed: {e}")
    
    async def _test_kucoin(self) -> bool:
        """Test KuCoin configuration"""
        api_key = os.getenv("KUCOIN_API_KEY")
        secret_key = os.getenv("KUCOIN_SECRET_KEY")
        passphrase = os.getenv("KUCOIN_PASSPHRASE")
        sandbox = os.getenv("KUCOIN_SANDBOX", "true").lower() == "true"
        
        if not (api_key and secret_key and passphrase):
            self.results["exchanges"]["details"].append("⚠️ KuCoin: Incomplete configuration")
            return False
        
        try:
            # Test basic configuration
            self.results["exchanges"]["details"].append(f"✅ KuCoin: Configuration complete (sandbox: {sandbox})")
            
            # Test connection (if ccxt is available)
            try:
                import ccxt
                exchange = ccxt.kucoin({
                    'apiKey': api_key,
                    'secret': secret_key,
                    'password': passphrase,
                    'sandbox': sandbox,
                })
                
                # Test connection
                balance = exchange.fetch_balance()
                self.results["exchanges"]["details"].append("✅ KuCoin: Connection successful")
                return True
                
            except ImportError:
                self.results["exchanges"]["details"].append("⚠️ KuCoin: ccxt not available for connection test")
                return True
            except Exception as e:
                self.results["exchanges"]["details"].append(f"❌ KuCoin: Connection failed - {e}")
                return False
                
        except Exception as e:
            self.results["exchanges"]["details"].append(f"❌ KuCoin: Test failed - {e}")
            return False
    
    async def _test_mexc(self) -> bool:
        """Test MEXC configuration"""
        api_key = os.getenv("MEXC_API_KEY")
        secret_key = os.getenv("MEXC_SECRET_KEY")
        sandbox = os.getenv("MEXC_SANDBOX", "true").lower() == "true"
        
        if not (api_key and secret_key):
            self.results["exchanges"]["details"].append("⚠️ MEXC: Incomplete configuration")
            return False
        
        try:
            self.results["exchanges"]["details"].append(f"✅ MEXC: Configuration complete (sandbox: {sandbox})")
            
            # Test connection (if ccxt is available)
            try:
                import ccxt
                exchange = ccxt.mexc({
                    'apiKey': api_key,
                    'secret': secret_key,
                    'sandbox': sandbox,
                })
                
                # Test connection
                balance = exchange.fetch_balance()
                self.results["exchanges"]["details"].append("✅ MEXC: Connection successful")
                return True
                
            except ImportError:
                self.results["exchanges"]["details"].append("⚠️ MEXC: ccxt not available for connection test")
                return True
            except Exception as e:
                self.results["exchanges"]["details"].append(f"❌ MEXC: Connection failed - {e}")
                return False
                
        except Exception as e:
            self.results["exchanges"]["details"].append(f"❌ MEXC: Test failed - {e}")
            return False
    
    async def _test_binance(self) -> bool:
        """Test Binance configuration"""
        api_key = os.getenv("BINANCE_API_KEY")
        secret_key = os.getenv("BINANCE_SECRET_KEY")
        testnet = os.getenv("BINANCE_TESTNET", "true").lower() == "true"
        
        if not (api_key and secret_key):
            self.results["exchanges"]["details"].append("⚠️ Binance: Incomplete configuration")
            return False
        
        try:
            self.results["exchanges"]["details"].append(f"✅ Binance: Configuration complete (testnet: {testnet})")
            return True
        except Exception as e:
            self.results["exchanges"]["details"].append(f"❌ Binance: Test failed - {e}")
            return False
    
    async def _test_database(self):
        """Test database configuration"""
        print("🗄️ Testing Database Configuration...")
        
        try:
            db_url = os.getenv("DATABASE_URL", "sqlite:///trading_bot.db")
            self.results["database"]["details"].append(f"ℹ️ Database URL: {db_url}")
            
            if db_url.startswith("sqlite:///"):
                # Test SQLite database
                db_path = db_url.replace("sqlite:///", "")
                db_dir = os.path.dirname(db_path) if os.path.dirname(db_path) else "."
                
                if os.path.exists(db_dir):
                    self.results["database"]["details"].append(f"✅ Database directory exists: {db_dir}")
                else:
                    self.results["database"]["details"].append(f"⚠️ Database directory missing: {db_dir}")
                
                # Test database connection
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    conn.execute("SELECT 1")
                    conn.close()
                    self.results["database"]["details"].append("✅ Database connection successful")
                    self.results["database"]["status"] = "success"
                except Exception as e:
                    self.results["database"]["details"].append(f"❌ Database connection failed: {e}")
                    self.results["database"]["status"] = "error"
            else:
                self.results["database"]["details"].append("ℹ️ Non-SQLite database - connection test skipped")
                self.results["database"]["status"] = "success"
            
            # Test backup settings
            backup_enabled = os.getenv("BACKUP_ENABLED", "true").lower() == "true"
            if backup_enabled:
                backup_location = os.getenv("BACKUP_LOCATION", "./backups/")
                if os.path.exists(backup_location):
                    self.results["database"]["details"].append(f"✅ Backup directory exists: {backup_location}")
                else:
                    self.results["database"]["details"].append(f"⚠️ Backup directory missing: {backup_location}")
            
        except Exception as e:
            self.results["database"]["status"] = "error"
            self.results["database"]["details"].append(f"❌ Database test failed: {e}")
    
    async def _test_security(self):
        """Test security configuration"""
        print("🔒 Testing Security Configuration...")
        
        try:
            # Test encryption key
            encryption_key = os.getenv("ENCRYPTION_KEY")
            if encryption_key:
                if len(encryption_key) >= 32:
                    self.results["security"]["details"].append("✅ Encryption key is properly configured")
                else:
                    self.results["security"]["details"].append("⚠️ Encryption key is too short")
            else:
                self.results["security"]["details"].append("⚠️ No encryption key configured")
            
            # Test rate limiting
            rate_limit = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
            self.results["security"]["details"].append(f"ℹ️ Rate limiting: {'enabled' if rate_limit else 'disabled'}")
            
            # Test 2FA
            enable_2fa = os.getenv("ENABLE_2FA", "false").lower() == "true"
            self.results["security"]["details"].append(f"ℹ️ 2FA: {'enabled' if enable_2fa else 'disabled'}")
            
            # Test session timeout
            try:
                timeout = int(os.getenv("SESSION_TIMEOUT", "3600"))
                if 300 <= timeout <= 86400:
                    self.results["security"]["details"].append(f"✅ Session timeout: {timeout}s")
                else:
                    self.results["security"]["details"].append(f"⚠️ Session timeout unusual: {timeout}s")
            except ValueError:
                self.results["security"]["details"].append("❌ Invalid session timeout")
            
            self.results["security"]["status"] = "success"
            
        except Exception as e:
            self.results["security"]["status"] = "error"
            self.results["security"]["details"].append(f"❌ Security test failed: {e}")
    
    def _calculate_overall_score(self):
        """Calculate overall configuration score"""
        scores = {
            "success": 3,
            "warning": 2,
            "error": 0,
            "unknown": 0
        }
        
        total_score = 0
        max_score = 0
        
        for category, result in self.results.items():
            if category != "overall":
                status = result["status"]
                total_score += scores.get(status, 0)
                max_score += 3
        
        if max_score > 0:
            percentage = (total_score / max_score) * 100
            self.results["overall"]["score"] = round(percentage, 1)
            
            if percentage >= 80:
                self.results["overall"]["status"] = "excellent"
            elif percentage >= 60:
                self.results["overall"]["status"] = "good"
            elif percentage >= 40:
                self.results["overall"]["status"] = "fair"
            else:
                self.results["overall"]["status"] = "poor"
    
    def _display_results(self):
        """Display test results"""
        print("\n" + "=" * 50)
        print("📊 Configuration Test Results")
        print("=" * 50)
        
        # Display category results
        for category, result in self.results.items():
            if category == "overall":
                continue
                
            status = result["status"]
            emoji = {"success": "✅", "warning": "⚠️", "error": "❌", "unknown": "❓"}
            
            print(f"\n{emoji.get(status, '❓')} {category.title()}: {status.upper()}")
            for detail in result["details"]:
                print(f"  {detail}")
        
        # Display overall score
        overall = self.results["overall"]
        score_emoji = {
            "excellent": "🌟",
            "good": "✅",
            "fair": "⚠️",
            "poor": "❌"
        }
        
        print(f"\n{score_emoji.get(overall['status'], '❓')} Overall Score: {overall['score']}% ({overall['status'].upper()})")
        
        # Recommendations
        print("\n💡 Recommendations:")
        if overall["score"] >= 80:
            print("  • Configuration looks great! You're ready to start trading.")
        elif overall["score"] >= 60:
            print("  • Configuration is mostly good. Address warnings before live trading.")
        else:
            print("  • Configuration needs improvement. Fix errors before proceeding.")
        
        print("\n📚 Next Steps:")
        print("  • Review any errors or warnings above")
        print("  • Test with paper trading first")
        print("  • Start the bot: python simple_bot.py")


async def main():
    """Main test function"""
    try:
        tester = ConfigTester()
        results = await tester.run_all_tests()
        
        # Run validation as well
        print("\n🔍 Running Configuration Validation...")
        validation_result = validate_config()
        
        if validation_result.errors:
            print("❌ Validation Errors:")
            for error in validation_result.errors:
                print(f"  • {error}")
        
        if validation_result.warnings:
            print("⚠️ Validation Warnings:")
            for warning in validation_result.warnings:
                print(f"  • {warning}")
        
        # Exit with appropriate code
        if results["overall"]["status"] in ["excellent", "good"]:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n❌ Tests cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
