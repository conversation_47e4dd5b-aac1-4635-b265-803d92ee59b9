#!/usr/bin/env python3
"""
Test Script voor Permissions & Roles Systeem
Uitgebreide tests van alle permission functionaliteiten
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from core.permissions_manager import get_permissions_manager, PermissionLevel, ResourceType
from core.permissions_decorators import *
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PermissionsTestSuite:
    """Comprehensive test suite for permissions system"""
    
    def __init__(self):
        self.permissions_manager = get_permissions_manager()
        self.test_results = {
            "passed": 0,
            "failed": 0,
            "errors": []
        }
    
    def run_all_tests(self):
        """Run all permission tests"""
        print("🧪 Testing Permissions & Roles System")
        print("=" * 50)
        
        # Core functionality tests
        self.test_role_assignment()
        self.test_permission_checking()
        self.test_temporary_permissions()
        self.test_permission_revocation()
        self.test_role_inheritance()
        
        # Decorator tests
        self.test_decorators()
        
        # Audit tests
        self.test_audit_logging()
        
        # Performance tests
        self.test_performance()
        
        # Display results
        self.display_results()
    
    def test_role_assignment(self):
        """Test role assignment functionality"""
        print("\n👤 Testing Role Assignment...")
        
        test_user_id = "test_user_123"
        
        try:
            # Test assigning valid role
            success = self.permissions_manager.assign_role(test_user_id, "user")
            if success:
                self.assert_true(True, "Role assignment successful")
                
                # Verify role was assigned
                user_role = self.permissions_manager.get_user_role(test_user_id)
                self.assert_equal(user_role, "user", "Role correctly assigned")
            else:
                self.assert_true(False, "Role assignment failed")
            
            # Test assigning invalid role
            success = self.permissions_manager.assign_role(test_user_id, "invalid_role")
            self.assert_true(not success, "Invalid role assignment correctly rejected")
            
            # Test role upgrade
            success = self.permissions_manager.assign_role(test_user_id, "trader")
            if success:
                user_role = self.permissions_manager.get_user_role(test_user_id)
                self.assert_equal(user_role, "trader", "Role upgrade successful")
            
        except Exception as e:
            self.record_error(f"Role assignment test failed: {e}")
    
    def test_permission_checking(self):
        """Test permission checking functionality"""
        print("\n🔐 Testing Permission Checking...")
        
        test_user_id = "test_user_456"
        
        try:
            # Assign trader role
            self.permissions_manager.assign_role(test_user_id, "trader")
            
            # Test valid permission
            has_perm, reason = self.permissions_manager.has_permission(test_user_id, "trading.view")
            self.assert_true(has_perm, f"Valid permission check: {reason}")
            
            # Test invalid permission
            has_perm, reason = self.permissions_manager.has_permission(test_user_id, "system.critical_config")
            self.assert_true(not has_perm, f"Invalid permission correctly denied: {reason}")
            
            # Test permission for different roles
            self.permissions_manager.assign_role(test_user_id, "admin")
            has_perm, reason = self.permissions_manager.has_permission(test_user_id, "users.manage")
            self.assert_true(has_perm, f"Admin permission check: {reason}")
            
        except Exception as e:
            self.record_error(f"Permission checking test failed: {e}")
    
    def test_temporary_permissions(self):
        """Test temporary permissions functionality"""
        print("\n⏰ Testing Temporary Permissions...")
        
        test_user_id = "test_user_789"
        
        try:
            # Assign basic role
            self.permissions_manager.assign_role(test_user_id, "user")
            
            # Grant temporary permission
            success = self.permissions_manager.grant_temporary_permission(
                test_user_id, "analytics.advanced", duration_hours=1
            )
            self.assert_true(success, "Temporary permission granted")
            
            # Check temporary permission
            has_perm, reason = self.permissions_manager.has_permission(test_user_id, "analytics.advanced")
            self.assert_true(has_perm, f"Temporary permission active: {reason}")
            
            # Check permission list includes temporary permission
            permissions = self.permissions_manager.get_user_permissions_list(test_user_id)
            self.assert_true("analytics.advanced" in permissions, "Temporary permission in list")
            
        except Exception as e:
            self.record_error(f"Temporary permissions test failed: {e}")
    
    def test_permission_revocation(self):
        """Test permission revocation functionality"""
        print("\n🚫 Testing Permission Revocation...")
        
        test_user_id = "test_user_revoke"
        
        try:
            # Assign trader role
            self.permissions_manager.assign_role(test_user_id, "trader")
            
            # Verify user has trading permission
            has_perm, _ = self.permissions_manager.has_permission(test_user_id, "trading.execute")
            self.assert_true(has_perm, "User initially has trading permission")
            
            # Revoke trading permission
            success = self.permissions_manager.revoke_permission(test_user_id, "trading.execute")
            self.assert_true(success, "Permission revocation successful")
            
            # Verify permission is revoked
            has_perm, reason = self.permissions_manager.has_permission(test_user_id, "trading.execute")
            self.assert_true(not has_perm, f"Permission correctly revoked: {reason}")
            
        except Exception as e:
            self.record_error(f"Permission revocation test failed: {e}")
    
    def test_role_inheritance(self):
        """Test role information and inheritance"""
        print("\n🏗️ Testing Role Information...")
        
        try:
            # Test getting role info
            role_info = self.permissions_manager.get_role_info("trader")
            self.assert_true(role_info is not None, "Role info retrieved")
            
            if role_info:
                self.assert_true("permissions" in role_info, "Role has permissions")
                self.assert_true("max_daily_trades" in role_info, "Role has trading limits")
                self.assert_true("trading_pairs_allowed" in role_info, "Role has trading pairs")
                
                # Check specific permissions
                permissions = role_info["permissions"]
                self.assert_true("trading.view" in permissions, "Trader has trading.view permission")
                self.assert_true("trading.execute" in permissions, "Trader has trading.execute permission")
            
        except Exception as e:
            self.record_error(f"Role inheritance test failed: {e}")
    
    def test_decorators(self):
        """Test permission decorators"""
        print("\n🎭 Testing Permission Decorators...")
        
        try:
            # Create mock message object
            class MockUser:
                def __init__(self, user_id):
                    self.id = user_id
            
            class MockMessage:
                def __init__(self, user_id):
                    self.from_user = MockUser(user_id)
            
            # Test user with trader role
            test_user_id = "decorator_test_user"
            self.permissions_manager.assign_role(test_user_id, "trader")
            
            # Test trading permission decorator
            @trading_permission
            def test_trading_function(message):
                return "Trading function executed"
            
            message = MockMessage(test_user_id)
            result = test_trading_function(message)
            self.assert_equal(result, "Trading function executed", "Trading decorator works")
            
            # Test admin only decorator with non-admin user
            @admin_only
            def test_admin_function(message):
                return "Admin function executed"
            
            result = test_admin_function(message)
            self.assert_true("Access denied" in result, "Admin decorator correctly blocks non-admin")
            
            # Test with admin user
            self.permissions_manager.assign_role(test_user_id, "admin")
            result = test_admin_function(message)
            self.assert_equal(result, "Admin function executed", "Admin decorator allows admin")
            
        except Exception as e:
            self.record_error(f"Decorators test failed: {e}")
    
    def test_audit_logging(self):
        """Test audit logging functionality"""
        print("\n📋 Testing Audit Logging...")
        
        test_user_id = "audit_test_user"
        
        try:
            # Assign role (should create audit log)
            self.permissions_manager.assign_role(test_user_id, "user")
            
            # Check permission (should create audit log)
            self.permissions_manager.has_permission(test_user_id, "trading.view")
            
            # Get audit logs
            audit_logs = self.permissions_manager.get_audit_logs(user_id=test_user_id, limit=10)
            self.assert_true(len(audit_logs) > 0, "Audit logs created")
            
            # Check audit log structure
            if audit_logs:
                log_entry = audit_logs[0]
                required_fields = ['user_id', 'action', 'permission', 'resource', 'granted', 'reason', 'created_at']
                for field in required_fields:
                    self.assert_true(field in log_entry, f"Audit log has {field} field")
            
        except Exception as e:
            self.record_error(f"Audit logging test failed: {e}")
    
    def test_performance(self):
        """Test performance of permissions system"""
        print("\n⚡ Testing Performance...")
        
        import time
        
        try:
            test_user_id = "perf_test_user"
            self.permissions_manager.assign_role(test_user_id, "trader")
            
            # Test permission checking performance
            start_time = time.time()
            for i in range(100):
                self.permissions_manager.has_permission(test_user_id, "trading.view")
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 100
            self.assert_true(avg_time < 0.01, f"Permission check performance: {avg_time:.4f}s per check")
            
            # Test caching effectiveness
            start_time = time.time()
            for i in range(100):
                self.permissions_manager.get_user_permissions_list(test_user_id)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 100
            self.assert_true(avg_time < 0.005, f"Cached permission list performance: {avg_time:.4f}s per call")
            
        except Exception as e:
            self.record_error(f"Performance test failed: {e}")
    
    # Helper methods
    def assert_true(self, condition, message):
        """Assert that condition is true"""
        if condition:
            print(f"  ✅ {message}")
            self.test_results["passed"] += 1
        else:
            print(f"  ❌ {message}")
            self.test_results["failed"] += 1
    
    def assert_equal(self, actual, expected, message):
        """Assert that actual equals expected"""
        if actual == expected:
            print(f"  ✅ {message}")
            self.test_results["passed"] += 1
        else:
            print(f"  ❌ {message} (expected: {expected}, got: {actual})")
            self.test_results["failed"] += 1
    
    def record_error(self, error_message):
        """Record an error"""
        print(f"  ❌ {error_message}")
        self.test_results["errors"].append(error_message)
        self.test_results["failed"] += 1
    
    def display_results(self):
        """Display test results"""
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)
        
        total_tests = self.test_results["passed"] + self.test_results["failed"]
        pass_rate = (self.test_results["passed"] / total_tests * 100) if total_tests > 0 else 0
        
        print(f"✅ Passed: {self.test_results['passed']}")
        print(f"❌ Failed: {self.test_results['failed']}")
        print(f"📈 Pass Rate: {pass_rate:.1f}%")
        
        if self.test_results["errors"]:
            print(f"\n🚨 Errors ({len(self.test_results['errors'])}):")
            for error in self.test_results["errors"]:
                print(f"  • {error}")
        
        if pass_rate >= 90:
            print("\n🎉 Permissions system is working excellently!")
        elif pass_rate >= 70:
            print("\n✅ Permissions system is working well with minor issues")
        else:
            print("\n⚠️ Permissions system needs attention")


def main():
    """Run permissions tests"""
    try:
        test_suite = PermissionsTestSuite()
        test_suite.run_all_tests()
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
