#!/usr/bin/env python3
"""
Database Viewer Tool voor Trading Bot
Bekijk de inhoud van SQLite databases in een leesbaar formaat
"""

import sqlite3
import sys
from datetime import datetime
from pathlib import Path

def connect_db(db_path):
    """Verbind met database"""
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # Voor dict-achtige toegang
        return conn
    except Exception as e:
        print(f"❌ Error connecting to {db_path}: {e}")
        return None

def show_tables(conn, db_name):
    """Toon alle tabellen in database"""
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print(f"\n📊 **{db_name} Database Tables:**")
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"  📋 {table_name}: {count} records")
    
    return [table[0] for table in tables]

def show_table_data(conn, table_name, limit=10):
    """Toon data van een specifieke tabel"""
    cursor = conn.cursor()
    
    # Get table info
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    print(f"\n📋 **Table: {table_name}**")
    print("Columns:", ", ".join([col[1] for col in columns]))
    
    # Get data
    cursor.execute(f"SELECT * FROM {table_name} LIMIT {limit}")
    rows = cursor.fetchall()
    
    if not rows:
        print("  (No data)")
        return
    
    # Print data
    for i, row in enumerate(rows, 1):
        print(f"\n  Record {i}:")
        for key in row.keys():
            value = row[key]
            if isinstance(value, str) and len(value) > 100:
                value = value[:100] + "..."
            print(f"    {key}: {value}")

def show_recent_activity(conn):
    """Toon recente activiteit"""
    cursor = conn.cursor()
    
    print(f"\n🕐 **Recent Activity:**")
    
    # Recent trades
    try:
        cursor.execute("SELECT * FROM trades ORDER BY timestamp DESC LIMIT 5")
        trades = cursor.fetchall()
        if trades:
            print(f"  📈 Recent Trades ({len(trades)}):")
            for trade in trades:
                print(f"    {trade['timestamp']}: {trade['side']} {trade['amount']} {trade['symbol']} @ {trade['price']}")
        else:
            print("  📈 No recent trades")
    except:
        pass
    
    # Recent analysis
    try:
        cursor.execute("SELECT * FROM analysis ORDER BY timestamp DESC LIMIT 3")
        analyses = cursor.fetchall()
        if analyses:
            print(f"  🔍 Recent Analysis ({len(analyses)}):")
            for analysis in analyses:
                print(f"    {analysis['timestamp']}: {analysis['symbol']} - {analysis['recommendation']} (confidence: {analysis['confidence']})")
        else:
            print("  🔍 No recent analysis")
    except:
        pass
    
    # Recent alerts
    try:
        cursor.execute("SELECT * FROM alerts ORDER BY timestamp DESC LIMIT 3")
        alerts = cursor.fetchall()
        if alerts:
            print(f"  🚨 Recent Alerts ({len(alerts)}):")
            for alert in alerts:
                status = "✅" if alert['acknowledged'] else "🔔"
                print(f"    {status} {alert['timestamp']}: {alert['alert_type']} - {alert['message']}")
        else:
            print("  🚨 No recent alerts")
    except:
        pass

def main():
    """Main functie"""
    print("🗄️ **Trading Bot Database Viewer**")
    print("=" * 50)
    
    # Database paths
    databases = {
        "Trading Bot": "trading_bot.db",
        "User Data": "database/user_data.db"
    }
    
    for db_name, db_path in databases.items():
        if not Path(db_path).exists():
            print(f"⚠️ Database {db_path} not found")
            continue
            
        print(f"\n🔍 **Checking {db_name} Database**")
        conn = connect_db(db_path)
        if not conn:
            continue
            
        try:
            tables = show_tables(conn, db_name)
            
            # Show data for each table
            for table in tables:
                show_table_data(conn, table, limit=5)
            
            # Show recent activity for main database
            if db_name == "Trading Bot":
                show_recent_activity(conn)
                
        except Exception as e:
            print(f"❌ Error reading {db_name}: {e}")
        finally:
            conn.close()
    
    print(f"\n✅ Database check completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
