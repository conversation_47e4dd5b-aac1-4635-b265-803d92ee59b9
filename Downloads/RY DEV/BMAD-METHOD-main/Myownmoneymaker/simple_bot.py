#!/usr/bin/env python3
"""
Advanced Telegram Trading Bot - Met echte exchange verbindingen
Gebruikt telebot library voor Python 3.13 compatibiliteit
"""

import os
import sys
import time
from datetime import datetime

import ccxt
import telebot
from dotenv import load_dotenv

# Add database path
sys.path.append(os.path.join(os.path.dirname(__file__), "database"))
from user_management import UserManager

# Add core path for permissions
sys.path.append(os.path.join(os.path.dirname(__file__), "core"))
from permissions_decorators import *
from permissions_manager import get_permissions_manager

# Load environment variables
load_dotenv()

# Initialize managers
user_manager = UserManager()
permissions_manager = get_permissions_manager()

# Initialize new systems
try:
    from gamification_system import GamificationManager
    from premium_system import PremiumManager, SubscriptionTier
    from real_trading_system import RealTradingManager

    gamification_manager = GamificationManager()
    premium_manager = PremiumManager()
    real_trading_manager = RealTradingManager()

    print("✅ Gamification system loaded")
    print("✅ Premium system loaded")
    print("✅ Real trading system loaded")
except ImportError as e:
    print(f"⚠️ Could not load new systems: {e}")
    gamification_manager = None
    premium_manager = None
    real_trading_manager = None

# Bot token
BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
if not BOT_TOKEN:
    raise ValueError("TELEGRAM_BOT_TOKEN environment variable is required")

ADMIN_USER_ID_STR = os.getenv("TELEGRAM_ADMIN_USER_ID")
if not ADMIN_USER_ID_STR:
    raise ValueError("TELEGRAM_ADMIN_USER_ID environment variable is required")
ADMIN_USER_ID = int(ADMIN_USER_ID_STR)

# Additional admin users
ADDITIONAL_ADMIN_IDS = []
additional_ids_str = os.getenv("ADDITIONAL_ADMIN_IDS", "")
if additional_ids_str:
    try:
        ADDITIONAL_ADMIN_IDS = [int(id.strip()) for id in additional_ids_str.split(",") if id.strip()]
        print(f"✅ Additional admin IDs loaded: {ADDITIONAL_ADMIN_IDS}")
    except ValueError:
        print("⚠️ Invalid format in ADDITIONAL_ADMIN_IDS, ignoring...")

# All admin IDs
ALL_ADMIN_IDS = [ADMIN_USER_ID] + ADDITIONAL_ADMIN_IDS

# Language settings
LANGUAGES = {
    "en": {
        "welcome_title": "🤖 Welcome to your Advanced Trading Bot!",
        "hello": "👋 Hello",
        "your_status": "👤 Your Status:",
        "features": "🔥 Features:",
        "quick_start": "📚 Quick Start:",
        "test_mode": "⚠️ Currently in TEST MODE - No real trades will be executed",
        "bot_status": "🕐 Bot Status: ✅ Online",
        "last_update": "📅 Last Update:",
        "market_analysis": "📊 Market Analysis",
        "portfolio": "💰 Portfolio",
        "start_trading": "🚀 START TRADING",
        "settings": "⚙️ Settings",
        "admin_panel": "🔧 Admin Panel",
        "back_to_main": "🏠 Back to Main Menu",
    },
    "nl": {
        "welcome_title": "🤖 Welkom bij je Geavanceerde Trading Bot!",
        "hello": "👋 Hallo",
        "your_status": "👤 Je Status:",
        "features": "🔥 Functies:",
        "quick_start": "📚 Snelle Start:",
        "test_mode": "⚠️ Momenteel in TEST MODUS - Geen echte trades worden uitgevoerd",
        "bot_status": "🕐 Bot Status: ✅ Online",
        "last_update": "📅 Laatste Update:",
        "market_analysis": "📊 Markt Analyse",
        "portfolio": "💰 Portfolio",
        "start_trading": "🚀 START TRADING",
        "settings": "⚙️ Instellingen",
        "admin_panel": "🔧 Admin Paneel",
        "back_to_main": "🏠 Terug naar Hoofdmenu",
    },
}

# Default language
DEFAULT_LANGUAGE = "nl"  # Dutch as default

# User language preferences (in-memory for now)
user_languages = {}


def get_user_language(user_id):
    """Get user's preferred language"""
    return user_languages.get(user_id, DEFAULT_LANGUAGE)


def set_user_language(user_id, language):
    """Set user's preferred language"""
    if language in LANGUAGES:
        user_languages[user_id] = language
        return True
    return False


def get_text(user_id, key):
    """Get localized text for user"""
    lang = get_user_language(user_id)
    return LANGUAGES[lang].get(key, LANGUAGES["en"].get(key, key))


# Create bot instance
bot = telebot.TeleBot(BOT_TOKEN)

# Exchange configuration
EXCHANGES = {}


def init_exchanges():
    """Initialize exchange connections"""
    global EXCHANGES
    try:
        # KuCoin
        if os.getenv("KUCOIN_API_KEY"):
            EXCHANGES["kucoin"] = ccxt.kucoin(
                {
                    "apiKey": os.getenv("KUCOIN_API_KEY"),
                    "secret": os.getenv("KUCOIN_SECRET_KEY"),
                    "password": os.getenv("KUCOIN_PASSPHRASE"),
                    "sandbox": os.getenv("KUCOIN_SANDBOX", "true").lower() == "true",
                    "enableRateLimit": True,
                }
            )
            print("✅ KuCoin connected")

        # MEXC
        if os.getenv("MEXC_API_KEY"):
            EXCHANGES["mexc"] = ccxt.mexc(
                {
                    "apiKey": os.getenv("MEXC_API_KEY"),
                    "secret": os.getenv("MEXC_SECRET_KEY"),
                    "sandbox": os.getenv("MEXC_SANDBOX", "true").lower() == "true",
                    "enableRateLimit": True,
                }
            )
            print("✅ MEXC connected")

    except Exception as e:
        print(f"❌ Exchange connection error: {e}")


# Initialize exchanges on startup
init_exchanges()


# Helper functions for trading
def get_crypto_price(symbol="BTC/USDT", exchange_name="kucoin"):
    """Get real crypto price from exchange"""
    try:
        if exchange_name in EXCHANGES:
            exchange = EXCHANGES[exchange_name]
            ticker = exchange.fetch_ticker(symbol)
            return {
                "price": ticker["last"],
                "change_24h": ticker["percentage"],
                "volume": ticker["baseVolume"],
                "high_24h": ticker["high"],
                "low_24h": ticker["low"],
            }
    except Exception as e:
        print(f"❌ Error fetching price for {symbol}: {e}")
        return None


def get_account_balance(exchange_name="kucoin"):
    """Get account balance from exchange"""
    try:
        if exchange_name in EXCHANGES:
            exchange = EXCHANGES[exchange_name]
            balance = exchange.fetch_balance()
            return balance
    except Exception as e:
        print(f"❌ Error fetching balance: {e}")
        return None


def place_buy_order(symbol, amount, exchange_name="kucoin"):
    """Place a buy order (TEST MODE ONLY)"""
    try:
        if os.getenv("TEST_MODE", "true").lower() == "true":
            # Simulate order in test mode
            price = get_crypto_price(symbol, exchange_name)
            if price:
                return {
                    "id": f"test_order_{int(time.time())}",
                    "symbol": symbol,
                    "amount": amount,
                    "price": price["price"],
                    "cost": float(amount) * price["price"],
                    "status": "filled",
                    "type": "market",
                    "side": "buy",
                    "timestamp": datetime.now().isoformat(),
                }
        else:
            # Real order (DANGEROUS - only when ready!)
            if exchange_name in EXCHANGES:
                exchange = EXCHANGES[exchange_name]
                order = exchange.create_market_buy_order(symbol, amount)
                return order
    except Exception as e:
        print(f"❌ Error placing buy order: {e}")
        return None


def format_price(price):
    """Format price for display"""
    if price is None:
        return "N/A"
    if price >= 1:
        return f"${price:,.2f}"
    else:
        return f"${price:.6f}"


def create_main_menu(user_id=None):
    """Create main menu inline keyboard with localized text"""
    markup = telebot.types.InlineKeyboardMarkup(row_width=2)

    # Get user language
    lang = get_user_language(user_id) if user_id else DEFAULT_LANGUAGE

    if lang == "nl":
        btn1 = telebot.types.InlineKeyboardButton("📊 Live Prijzen", callback_data="live_prices")
        btn2 = telebot.types.InlineKeyboardButton("💰 Portfolio", callback_data="portfolio")
        btn3 = telebot.types.InlineKeyboardButton("🛒 Koop Crypto", callback_data="buy_crypto")
        btn4 = telebot.types.InlineKeyboardButton("📈 Markt Analyse", callback_data="market_analysis")
        btn5 = telebot.types.InlineKeyboardButton("⚙️ Instellingen", callback_data="settings")
        btn6 = telebot.types.InlineKeyboardButton("❓ Help", callback_data="help")
        btn7 = telebot.types.InlineKeyboardButton("🇬🇧 English", callback_data="lang_en")
    else:
        btn1 = telebot.types.InlineKeyboardButton("📊 Live Prices", callback_data="live_prices")
        btn2 = telebot.types.InlineKeyboardButton("💰 Portfolio", callback_data="portfolio")
        btn3 = telebot.types.InlineKeyboardButton("🛒 Buy Crypto", callback_data="buy_crypto")
        btn4 = telebot.types.InlineKeyboardButton("📈 Market Analysis", callback_data="market_analysis")
        btn5 = telebot.types.InlineKeyboardButton("⚙️ Settings", callback_data="settings")
        btn6 = telebot.types.InlineKeyboardButton("❓ Help", callback_data="help")
        btn7 = telebot.types.InlineKeyboardButton("🇳🇱 Nederlands", callback_data="lang_nl")

    markup.add(btn1, btn2)
    markup.add(btn3, btn4)
    markup.add(btn5, btn6)

    # Add gamification and premium buttons
    if lang == "nl":
        btn8 = telebot.types.InlineKeyboardButton("🏆 Leaderboard", callback_data="leaderboard")
        btn9 = telebot.types.InlineKeyboardButton("💎 Premium", callback_data="premium_info")
        btn10 = telebot.types.InlineKeyboardButton("🔴 Real Trading", callback_data="real_trading")
    else:
        btn8 = telebot.types.InlineKeyboardButton("🏆 Leaderboard", callback_data="leaderboard")
        btn9 = telebot.types.InlineKeyboardButton("💎 Premium", callback_data="premium_info")
        btn10 = telebot.types.InlineKeyboardButton("🔴 Real Trading", callback_data="real_trading")

    markup.add(btn8, btn9)
    markup.add(btn10)
    markup.add(btn7)

    return markup


def create_buy_crypto_menu():
    """Create crypto buying menu"""
    markup = telebot.types.InlineKeyboardMarkup(row_width=2)

    btn1 = telebot.types.InlineKeyboardButton("₿ Buy BTC", callback_data="buy_BTC/USDT")
    btn2 = telebot.types.InlineKeyboardButton("Ξ Buy ETH", callback_data="buy_ETH/USDT")
    btn3 = telebot.types.InlineKeyboardButton("🔸 Buy BNB", callback_data="buy_BNB/USDT")
    btn4 = telebot.types.InlineKeyboardButton("🌕 Buy ADA", callback_data="buy_ADA/USDT")
    btn5 = telebot.types.InlineKeyboardButton("☀️ Buy SOL", callback_data="buy_SOL/USDT")
    btn6 = telebot.types.InlineKeyboardButton("💎 Buy MATIC", callback_data="buy_MATIC/USDT")
    btn_back = telebot.types.InlineKeyboardButton("🏠 Back to Main Menu", callback_data="back_to_main")

    markup.add(btn1, btn2)
    markup.add(btn3, btn4)
    markup.add(btn5, btn6)
    markup.add(btn_back)

    return markup


def create_buy_amount_menu(symbol):
    """Create amount selection menu for buying"""
    markup = telebot.types.InlineKeyboardMarkup(row_width=3)

    # Get current price for calculations
    price_data = get_crypto_price(symbol)
    if price_data:
        price = price_data["price"]

        # Different amount options based on price
        if symbol == "BTC/USDT":
            amounts = ["$10", "$25", "$50", "$100", "$250", "$500"]
        else:
            amounts = ["$5", "$10", "$25", "$50", "$100", "$250"]

        buttons = []
        for amount in amounts:
            usd_amount = float(amount.replace("$", ""))
            crypto_amount = usd_amount / price
            btn = telebot.types.InlineKeyboardButton(
                f"{amount} (~{crypto_amount:.6f})", callback_data=f"confirm_buy_{symbol}_{usd_amount}"
            )
            buttons.append(btn)

        # Add buttons in rows of 3
        for i in range(0, len(buttons), 3):
            markup.add(*buttons[i : i + 3])

    btn_back = telebot.types.InlineKeyboardButton("🔙 Back", callback_data="buy_crypto")
    markup.add(btn_back)

    return markup


def create_admin_menu():
    """Create admin panel menu"""
    markup = telebot.types.InlineKeyboardMarkup(row_width=2)

    btn1 = telebot.types.InlineKeyboardButton("👥 Manage Users", callback_data="admin_manage_users")
    btn2 = telebot.types.InlineKeyboardButton("🔑 Make Admin", callback_data="admin_make_admin")
    btn3 = telebot.types.InlineKeyboardButton("📊 View Stats", callback_data="admin_view_stats")
    btn4 = telebot.types.InlineKeyboardButton("🔧 System Settings", callback_data="admin_settings")
    btn5 = telebot.types.InlineKeyboardButton("📋 Activity Log", callback_data="admin_activity_log")
    btn6 = telebot.types.InlineKeyboardButton("🚨 Emergency Stop", callback_data="admin_emergency_stop")
    btn_back = telebot.types.InlineKeyboardButton("🏠 Back to Main Menu", callback_data="back_to_main")

    markup.add(btn1, btn2)
    markup.add(btn3, btn4)
    markup.add(btn5, btn6)
    markup.add(btn_back)

    return markup


def create_user_management_menu():
    """Create user management menu"""
    markup = telebot.types.InlineKeyboardMarkup(row_width=2)

    btn1 = telebot.types.InlineKeyboardButton("📋 List All Users", callback_data="admin_list_users")
    btn2 = telebot.types.InlineKeyboardButton("🔍 Search User", callback_data="admin_search_user")
    btn3 = telebot.types.InlineKeyboardButton("🔧 Edit User", callback_data="admin_edit_user")
    btn4 = telebot.types.InlineKeyboardButton("🚫 Ban User", callback_data="admin_ban_user")
    btn5 = telebot.types.InlineKeyboardButton("✅ Unban User", callback_data="admin_unban_user")
    btn6 = telebot.types.InlineKeyboardButton("💰 Set Limits", callback_data="admin_set_limits")
    btn_back = telebot.types.InlineKeyboardButton("🔙 Back to Admin", callback_data="admin_panel")

    markup.add(btn1, btn2)
    markup.add(btn3, btn4)
    markup.add(btn5, btn6)
    markup.add(btn_back)

    return markup


def check_user_permissions(telegram_id, required_permission):
    """Check if user has required permission using new permissions system"""
    has_perm, reason = permissions_manager.has_permission(str(telegram_id), required_permission)
    return has_perm


def register_user_if_new(message):
    """Register user if they're new to the system with permissions"""
    user = message.from_user
    existing_user = user_manager.get_user(user.id)

    if not existing_user:
        # Determine role based on admin list
        role = "super_admin" if user.id in ALL_ADMIN_IDS else "user"

        # Register in user manager
        success = user_manager.add_user(
            telegram_id=user.id, username=user.username, first_name=user.first_name, last_name=user.last_name, role=role
        )

        if success:
            # Also register in permissions system
            permissions_success = permissions_manager.assign_role(str(user.id), role)
            if permissions_success:
                print(f"✅ New user registered: {user.username} ({user.id}) as {role} with permissions")
            else:
                print(f"⚠️ User registered but permissions assignment failed: {user.username} ({user.id})")

            # Initialize gamification for new user
            if gamification_manager:
                gamification_manager.get_user_gamification(user.id)
                # Give welcome XP
                xp_result = gamification_manager.add_xp(user.id, 50, "Welkom bij de bot!")
                print(f"✅ Gamification initialized for user {user.id}")
        return success
    else:
        # Update last active
        user_manager.get_user(user.id)  # This updates last_active

        # Ensure user has permissions role assigned
        user_role = permissions_manager.get_user_role(str(user.id))
        if not user_role:
            # Assign default role if missing
            existing_role = existing_user.get("role", "user")
            permissions_manager.assign_role(str(user.id), existing_role)
            print(f"✅ Assigned missing permissions role to existing user: {user.username} ({user.id})")

        return True


def create_back_menu():
    """Create back to main menu keyboard"""
    markup = telebot.types.InlineKeyboardMarkup()
    btn = telebot.types.InlineKeyboardButton("🏠 Back to Main Menu", callback_data="back_to_main")
    markup.add(btn)
    return markup


def award_trading_xp(user_id: int, profit: float, is_successful: bool):
    """Award XP for trading activity"""
    if not gamification_manager:
        return None

    try:
        result = gamification_manager.record_trade(user_id, profit, is_successful)

        # Send notification about XP gained
        if result and result.get("xp_result"):
            xp_gained = result["xp_result"]["xp_gained"]
            level_up = result["xp_result"]["level_up"]

            lang = get_user_language(user_id)

            if lang == "nl":
                message_text = f"🎮 +{xp_gained} XP verdiend!"
                if level_up:
                    message_text += f"\n🎉 Level UP! Je bent nu level {result['xp_result']['level']}!"
            else:
                message_text = f"🎮 +{xp_gained} XP earned!"
                if level_up:
                    message_text += f"\n🎉 Level UP! You are now level {result['xp_result']['level']}!"

            # Check for new achievements
            if result.get("achievements"):
                for achievement in result["achievements"]:
                    if lang == "nl":
                        message_text += (
                            f"\n🏆 Nieuwe achievement: {achievement['name']} (+{achievement['xp_reward']} XP)"
                        )
                    else:
                        message_text += f"\n🏆 New achievement: {achievement['name']} (+{achievement['xp_reward']} XP)"

            try:
                bot.send_message(user_id, message_text)
            except:
                pass  # Don't fail if we can't send the message

        return result
    except Exception as e:
        print(f"Error awarding XP: {e}")
        return None


@bot.message_handler(commands=["demo_trade"])
def demo_trade_command(message):
    """Demo trade command to test XP system"""
    register_user_if_new(message)
    user = message.from_user

    # Simulate a profitable trade
    profit = 25.50  # €25.50 profit
    is_successful = True

    # Award XP
    result = award_trading_xp(user.id, profit, is_successful)

    lang = get_user_language(user.id)

    if lang == "nl":
        text = f"""
🎮 **Demo Trade Voltooid!**

💰 **Winst:** €{profit:.2f}
✅ **Status:** Succesvol

🎯 **XP Systeem Test:**
"""
    else:
        text = f"""
🎮 **Demo Trade Completed!**

💰 **Profit:** €{profit:.2f}
✅ **Status:** Successful

🎯 **XP System Test:**
"""

    if result:
        if lang == "nl":
            text += f"• XP verdiend: +{result['xp_result']['xp_gained']}\n"
            text += f"• Totaal XP: {result['xp_result']['total_xp']}\n"
            text += f"• Level: {result['xp_result']['level']}\n"
            if result["xp_result"]["level_up"]:
                text += "🎉 **LEVEL UP!**\n"
        else:
            text += f"• XP earned: +{result['xp_result']['xp_gained']}\n"
            text += f"• Total XP: {result['xp_result']['total_xp']}\n"
            text += f"• Level: {result['xp_result']['level']}\n"
            if result["xp_result"]["level_up"]:
                text += "🎉 **LEVEL UP!**\n"

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["start"])
def start_command(message):
    """Handle /start command"""
    # Register user if new
    register_user_if_new(message)

    user = message.from_user
    user_data = user_manager.get_user(user.id)

    status_text = user_data["role"].title() if user_data else "User"
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Get user language for localized text
    lang = get_user_language(user.id)

    if lang == "nl":
        welcome_text = f"""🤖 Welkom bij je Geavanceerde Trading Bot!

👋 Hallo {user.first_name}!

👤 Je Status: {status_text}

🔥 Functies:
• Multi-exchange trading (KuCoin, MEXC)
• AI-aangedreven marktanalyse
• Real-time waarschuwingen en monitoring
• Risicobeheer

📚 Snelle Start:
• Gebruik de knoppen hieronder om te navigeren
• Bekijk marktanalyse voor inzichten
• Bekijk je portfolio status
• Configureer trading instellingen

⚠️ Momenteel in TEST MODUS - Geen echte trades worden uitgevoerd

🕐 Bot Status: ✅ Online
📅 Laatste Update: {current_time}"""
    else:
        welcome_text = f"""🤖 Welcome to your Advanced Trading Bot!

👋 Hello {user.first_name}!

👤 Your Status: {status_text}

🔥 Features:
• Multi-exchange trading (KuCoin, MEXC)
• AI-powered market analysis
• Real-time alerts and monitoring
• Risk management

📚 Quick Start:
• Use the buttons below to navigate
• Check market analysis for insights
• View your portfolio status
• Configure trading settings

⚠️ Currently in TEST MODE - No real trades will be executed

🕐 Bot Status: ✅ Online
📅 Last Update: {current_time}"""

    bot.send_message(message.chat.id, welcome_text, reply_markup=create_main_menu(user.id))


@bot.message_handler(commands=["admin_panel", "admin"])
@auto_assign_role_on_first_use()
@require_any_role(["admin", "super_admin"])
def admin_panel_command(message):
    """Handle /admin_panel command with permissions"""
    register_user_if_new(message)

    user_data = user_manager.get_user(message.from_user.id)
    stats = user_manager.get_stats()

    admin_text = f"""
🔧 **Admin Panel**

👤 **Admin:** {message.from_user.first_name}
🎭 **Role:** {user_data["role"].title()}

📊 **System Statistics:**
• **Total Users:** {stats["total_users"]}
• **Active Users (24h):** {stats["active_users"]}
• **Admins:** {stats["role_distribution"].get("admin", 0) + stats["role_distribution"].get("super_admin", 0)}

🔧 **Available Functions:**
• User Management
• Role Assignment
• System Monitoring
• Activity Logs

⚡ **System Status:** 🟢 Online
"""

    bot.send_message(message.chat.id, admin_text, reply_markup=create_admin_menu(), parse_mode="Markdown")


@bot.message_handler(commands=["manage_users"])
@auto_assign_role_on_first_use()
@require_permission("users.manage")
def manage_users_command(message):
    """Handle /manage_users command with permissions"""
    register_user_if_new(message)

    users = user_manager.get_all_users()

    text = f"""
👥 **User Management**

📊 **Total Users:** {len(users)}

🔧 **Management Options:**
• View all users
• Search specific user
• Edit user permissions
• Ban/Unban users
• Set trading limits

👇 **Choose an action:**
"""

    bot.send_message(message.chat.id, text, reply_markup=create_user_management_menu(), parse_mode="Markdown")


@bot.message_handler(commands=["make_admin"])
@auto_assign_role_on_first_use()
@require_permission("users.create_admin")
def make_admin_command(message):
    """Handle /make_admin command with permissions"""
    register_user_if_new(message)

    text = """
🔑 **Make Admin**

📝 **Instructions:**
1. Forward a message from the user you want to make admin
2. Or use: `/make_admin @username`
3. Or use: `/make_admin user_id`

⚠️ **Warning:** Admin privileges include:
• User management access
• System settings access
• Trading oversight

🔒 **Only Super Admins can create new admins**
"""

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["help"])
def help_command(message):
    """Handle /help command"""
    lang = get_user_language(message.from_user.id)

    if lang == "nl":
        help_text = """
❓ **Help & Commando's**

📚 **Beschikbare Commando's:**
• `/start` - Toon hoofdmenu
• `/help` - Toon deze help
• `/myid` - Bekijk je User ID
• `/setup_admin` - Activeer admin rechten (alleen voor admins)

🎮 **Knop Functies:**
• **📊 Markt Analyse** - Bekijk marktdata
• **💰 Portfolio** - Controleer je saldo
• **🛒 Koop Crypto** - Crypto kopen
• **⚙️ Instellingen** - Configureer bot
• **📊 Live Prijzen** - Real-time prijzen

🆘 **Ondersteuning:**
• Contact: @InnovarsLabo
• Problemen: Rapporteer via admin
• Updates: Controleer aankondigingen

📖 **Documentatie:**
• Trading gids beschikbaar
• Risicobeheer tips
• Strategie uitleg

🌐 **Taal:**
• Gebruik de taalknop in het hoofdmenu om te wisselen tussen Nederlands en Engels
"""
    else:
        help_text = """
❓ **Help & Commands**

📚 **Available Commands:**
• `/start` - Show main menu
• `/help` - Show this help
• `/myid` - View your User ID
• `/setup_admin` - Activate admin rights (admins only)

🎮 **Button Functions:**
• **📊 Market Analysis** - View market data
• **💰 Portfolio** - Check your balance
• **🛒 Buy Crypto** - Buy cryptocurrency
• **⚙️ Settings** - Configure bot
• **📊 Live Prices** - Real-time prices

🆘 **Support:**
• Contact: @InnovarsLabo
• Issues: Report via admin
• Updates: Check announcements

📖 **Documentation:**
• Trading guide available
• Risk management tips
• Strategy explanations

🌐 **Language:**
• Use the language button in the main menu to switch between Dutch and English
"""

    bot.send_message(message.chat.id, help_text, parse_mode="Markdown")


@bot.message_handler(commands=["myid", "id"])
def get_user_id_command(message):
    """Get user's Telegram ID"""
    user = message.from_user

    text = f"""
🆔 **Your Telegram Information**

👤 **User ID:** `{user.id}`
📝 **Username:** @{user.username if user.username else "No username"}
🏷️ **First Name:** {user.first_name}
🏷️ **Last Name:** {user.last_name if user.last_name else "Not set"}

📋 **To add yourself as admin:**
1. Copy your User ID: `{user.id}`
2. Send it to the main admin
3. They can add you using: `/assign_role {user.id} super_admin`

🔐 **Current Status:** {"✅ Admin" if user.id in ALL_ADMIN_IDS else "👤 Regular User"}
"""

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["setup_admin"])
def setup_admin_command(message):
    """Setup command for new admin - only works if user is in ALL_ADMIN_IDS"""
    user = message.from_user

    if user.id not in ALL_ADMIN_IDS:
        text = f"""
❌ **Access Denied**

You are not authorized to use this command.

🆔 **Your User ID:** `{user.id}`

📝 **To get admin access:**
1. Contact the main administrator
2. Provide your User ID: `{user.id}`
3. Ask them to add you to the admin list

💡 **Alternative:** Ask an existing admin to run:
`/assign_role {user.id} super_admin`
"""
        bot.send_message(message.chat.id, text, parse_mode="Markdown")
        return

    # User is authorized, set them up as admin
    register_user_if_new(message)

    # Ensure they have super_admin role in permissions system
    success = permissions_manager.assign_role(str(user.id), "super_admin")

    if success:
        text = f"""
✅ **Admin Setup Complete!**

👤 **Welcome Admin:** {user.first_name}
🆔 **User ID:** `{user.id}`
🎭 **Role:** Super Administrator

🔧 **Available Commands:**
• `/admin_panel` - Access admin dashboard
• `/manage_users` - Manage users
• `/assign_role user_id role` - Assign roles
• `/grant_permission user_id permission hours` - Grant temporary permissions
• `/permissions` - View your permissions

🚀 **You now have full admin access to the trading bot!**
"""
    else:
        text = f"""
⚠️ **Partial Setup**

Your user account was created but there was an issue with permissions.
Please contact the main administrator.

🆔 **Your User ID:** `{user.id}`
"""

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["permissions"])
@auto_assign_role_on_first_use()
def permissions_command(message):
    """Show user's permissions"""
    register_user_if_new(message)

    user_id = str(message.from_user.id)
    user_role = permissions_manager.get_user_role(user_id)
    user_permissions = permissions_manager.get_user_permissions_list(user_id)

    if not user_role:
        bot.send_message(message.chat.id, "❌ No role assigned. Contact an administrator.", parse_mode="Markdown")
        return

    role_info = permissions_manager.get_role_info(user_role)

    text = f"""
🔐 **Your Permissions**

👤 **Role:** {role_info["display_name"] if role_info else user_role}
📝 **Description:** {role_info["description"] if role_info else "No description"}

🎯 **Your Permissions ({len(user_permissions)}):**
"""

    # Group permissions by category
    permission_groups = {}
    for perm in user_permissions:
        category = perm.split(".")[0]
        if category not in permission_groups:
            permission_groups[category] = []
        permission_groups[category].append(perm.split(".")[1])

    for category, perms in permission_groups.items():
        text += f"\n**{category.title()}:** {', '.join(perms)}"

    if role_info:
        text += "\n\n📊 **Trading Limits:**"
        if role_info.get("max_daily_trades"):
            text += f"\n• Max daily trades: {role_info['max_daily_trades']}"
        if role_info.get("max_position_size"):
            text += f"\n• Max position size: {role_info['max_position_size'] * 100:.1f}%"
        if role_info.get("trading_pairs_allowed"):
            text += f"\n• Allowed pairs: {', '.join(role_info['trading_pairs_allowed'])}"

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["assign_role"])
@auto_assign_role_on_first_use()
@require_permission("users.manage")
def assign_role_command(message):
    """Assign role to user (admin only)"""
    register_user_if_new(message)

    # Parse command: /assign_role user_id role_name
    parts = message.text.split()
    if len(parts) != 3:
        text = """
🔧 **Assign Role**

📝 **Usage:** `/assign_role user_id role_name`

🎭 **Available Roles:**
• `user` - Basic user permissions
• `trader` - Advanced trading permissions
• `analyst` - Analytics and research permissions
• `admin` - Administrative permissions
• `super_admin` - Full system access

📋 **Example:** `/assign_role 123456789 trader`
"""
        bot.send_message(message.chat.id, text, parse_mode="Markdown")
        return

    target_user_id = parts[1]
    new_role = parts[2]

    # Check if role exists
    role_info = permissions_manager.get_role_info(new_role)
    if not role_info:
        bot.send_message(message.chat.id, f"❌ Role '{new_role}' does not exist.", parse_mode="Markdown")
        return

    # Assign role
    success = permissions_manager.assign_role(target_user_id, new_role)
    if success:
        # Also update in user manager
        user_manager.update_user_role(int(target_user_id), new_role)

        text = f"""
✅ **Role Assigned Successfully**

👤 **User ID:** {target_user_id}
🎭 **New Role:** {role_info["display_name"]}
📝 **Description:** {role_info["description"]}

🔄 **Changes take effect immediately**
"""
        bot.send_message(message.chat.id, text, parse_mode="Markdown")
    else:
        bot.send_message(
            message.chat.id, f"❌ Failed to assign role '{new_role}' to user {target_user_id}", parse_mode="Markdown"
        )


@bot.message_handler(commands=["grant_permission"])
@auto_assign_role_on_first_use()
@super_admin_only
def grant_permission_command(message):
    """Grant temporary permission to user (super admin only)"""
    register_user_if_new(message)

    # Parse command: /grant_permission user_id permission_name hours
    parts = message.text.split()
    if len(parts) < 3 or len(parts) > 4:
        text = """
🔑 **Grant Temporary Permission**

📝 **Usage:** `/grant_permission user_id permission_name [hours]`

⏰ **Default duration:** 24 hours

📋 **Examples:**
• `/grant_permission 123456789 trading.manage`
• `/grant_permission 123456789 analytics.advanced 48`

🔐 **Common Permissions:**
• `trading.execute` - Execute trades
• `trading.manage` - Manage trading settings
• `analytics.advanced` - Advanced analytics
• `users.view` - View user information
"""
        bot.send_message(message.chat.id, text, parse_mode="Markdown")
        return

    target_user_id = parts[1]
    permission = parts[2]
    hours = int(parts[3]) if len(parts) == 4 else 24

    # Grant temporary permission
    success = permissions_manager.grant_temporary_permission(target_user_id, permission, hours)
    if success:
        text = f"""
✅ **Temporary Permission Granted**

👤 **User ID:** {target_user_id}
🔑 **Permission:** {permission}
⏰ **Duration:** {hours} hours

🔄 **Permission is active immediately**
"""
        bot.send_message(message.chat.id, text, parse_mode="Markdown")
    else:
        bot.send_message(
            message.chat.id,
            f"❌ Failed to grant permission '{permission}' to user {target_user_id}",
            parse_mode="Markdown",
        )


@bot.message_handler(commands=["profile", "stats"])
def profile_command(message):
    """Show user profile with gamification stats"""
    if not gamification_manager:
        bot.send_message(message.chat.id, "❌ Gamification system not available")
        return

    register_user_if_new(message)
    user = message.from_user
    lang = get_user_language(user.id)

    # Update daily streak
    streak_result = gamification_manager.update_daily_streak(user.id)

    # Get user data
    user_data = gamification_manager.get_user_gamification(user.id)
    subscription = premium_manager.get_user_subscription(user.id) if premium_manager else None

    # Calculate XP for next level
    next_level_xp = gamification_manager.xp_for_next_level(user_data["level"])
    xp_progress = user_data["xp"] - ((user_data["level"] - 1) ** 2) * 100
    xp_needed = next_level_xp - user_data["xp"]

    if lang == "nl":
        text = f"""
👤 **Profiel van {user.first_name}**

🎮 **Gamification Stats:**
• Level: {user_data["level"]} ⭐
• XP: {user_data["xp"]:,} ({xp_progress}/{next_level_xp - ((user_data["level"] - 1) ** 2) * 100})
• XP tot volgend level: {xp_needed:,}

📊 **Trading Stats:**
• Totale trades: {user_data["total_trades"]}
• Succesvolle trades: {user_data["successful_trades"]}
• Success rate: {(user_data["successful_trades"] / user_data["total_trades"] * 100) if user_data["total_trades"] > 0 else 0:.1f}%
• Totale winst: €{user_data["total_profit"]:.2f}

🔥 **Activiteit:**
• Huidige streak: {user_data["streak_days"]} dagen
• Laatste activiteit: {user_data["last_activity"]}

🏆 **Achievements:** {len(user_data["achievements"])}
🎖️ **Badges:** {", ".join(user_data["badges"]) if user_data["badges"] else "Geen badges"}

💎 **Subscription:** {subscription["tier"].title() if subscription else "Free"}
"""
    else:
        text = f"""
👤 **Profile of {user.first_name}**

🎮 **Gamification Stats:**
• Level: {user_data["level"]} ⭐
• XP: {user_data["xp"]:,} ({xp_progress}/{next_level_xp - ((user_data["level"] - 1) ** 2) * 100})
• XP to next level: {xp_needed:,}

📊 **Trading Stats:**
• Total trades: {user_data["total_trades"]}
• Successful trades: {user_data["successful_trades"]}
• Success rate: {(user_data["successful_trades"] / user_data["total_trades"] * 100) if user_data["total_trades"] > 0 else 0:.1f}%
• Total profit: €{user_data["total_profit"]:.2f}

🔥 **Activity:**
• Current streak: {user_data["streak_days"]} days
• Last activity: {user_data["last_activity"]}

🏆 **Achievements:** {len(user_data["achievements"])}
🎖️ **Badges:** {", ".join(user_data["badges"]) if user_data["badges"] else "No badges"}

💎 **Subscription:** {subscription["tier"].title() if subscription else "Free"}
"""

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.message_handler(commands=["leaderboard"])
def leaderboard_command(message):
    """Show leaderboard"""
    if not gamification_manager:
        bot.send_message(message.chat.id, "❌ Gamification system not available")
        return

    register_user_if_new(message)
    lang = get_user_language(message.from_user.id)

    # Get leaderboards
    xp_leaderboard = gamification_manager.get_leaderboard("weekly", "xp", 10)
    profit_leaderboard = gamification_manager.get_leaderboard("weekly", "profit", 5)

    if lang == "nl":
        text = """
🏆 **Leaderboard**

⭐ **Top XP (Deze Week):**
"""
        for i, entry in enumerate(xp_leaderboard, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            text += f"{medal} {entry['username']}: {entry['value']:,} XP\n"

        text += "\n💰 **Top Winst (Deze Week):**\n"
        for i, entry in enumerate(profit_leaderboard, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            text += f"{medal} {entry['username']}: €{entry['value']:.2f}\n"
    else:
        text = """
🏆 **Leaderboard**

⭐ **Top XP (This Week):**
"""
        for i, entry in enumerate(xp_leaderboard, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            text += f"{medal} {entry['username']}: {entry['value']:,} XP\n"

        text += "\n💰 **Top Profit (This Week):**\n"
        for i, entry in enumerate(profit_leaderboard, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
            text += f"{medal} {entry['username']}: €{entry['value']:.2f}\n"

    bot.send_message(message.chat.id, text, parse_mode="Markdown")


@bot.callback_query_handler(func=lambda call: True)
def callback_handler(call):
    """Handle button callbacks"""
    user = call.from_user
    data = call.data

    print(f"Button pressed: {data} by user {user.id} (@{user.username})")

    # Handle language switching
    if data == "lang_nl":
        set_user_language(user.id, "nl")
        # Refresh main menu with Dutch
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        welcome_text = f"""🤖 Welkom bij je Geavanceerde Trading Bot!

👋 Hallo {user.first_name}!

🔥 Functies:
• Multi-exchange trading (KuCoin, MEXC)
• AI-aangedreven marktanalyse
• Real-time waarschuwingen en monitoring
• Risicobeheer

📚 Snelle Start:
• Gebruik de knoppen hieronder om te navigeren
• Bekijk marktanalyse voor inzichten
• Bekijk je portfolio status
• Configureer trading instellingen

⚠️ Momenteel in TEST MODUS - Geen echte trades worden uitgevoerd

🕐 Bot Status: ✅ Online
📅 Laatste Update: {current_time}"""
        bot.edit_message_text(
            welcome_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=create_main_menu(user.id),
        )
        return

    elif data == "lang_en":
        set_user_language(user.id, "en")
        # Refresh main menu with English
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        welcome_text = f"""🤖 Welcome to your Advanced Trading Bot!

👋 Hello {user.first_name}!

🔥 Features:
• Multi-exchange trading (KuCoin, MEXC)
• AI-powered market analysis
• Real-time alerts and monitoring
• Risk management

📚 Quick Start:
• Use the buttons below to navigate
• Check market analysis for insights
• View your portfolio status
• Configure trading settings

⚠️ Currently in TEST MODE - No real trades will be executed

🕐 Bot Status: ✅ Online
📅 Last Update: {current_time}"""
        bot.edit_message_text(
            welcome_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=create_main_menu(user.id),
        )
        return

    elif data == "back_to_main":
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        lang = get_user_language(user.id)

        if lang == "nl":
            welcome_text = f"""🤖 Welkom bij je Geavanceerde Trading Bot!

👋 Hallo {user.first_name}!

🔥 Functies:
• Multi-exchange trading (KuCoin, MEXC)
• AI-aangedreven marktanalyse
• Real-time waarschuwingen en monitoring
• Risicobeheer

📚 Snelle Start:
• Gebruik de knoppen hieronder om te navigeren
• Bekijk marktanalyse voor inzichten
• Bekijk je portfolio status
• Configureer trading instellingen

⚠️ Momenteel in TEST MODUS - Geen echte trades worden uitgevoerd

🕐 Bot Status: ✅ Online
📅 Laatste Update: {current_time}"""
        else:
            welcome_text = f"""🤖 Welcome to your Advanced Trading Bot!

👋 Hello {user.first_name}!

🔥 Features:
• Multi-exchange trading (KuCoin, MEXC)
• AI-powered market analysis
• Real-time alerts and monitoring
• Risk management

📚 Quick Start:
• Use the buttons below to navigate
• Check market analysis for insights
• View your portfolio status
• Configure trading settings

⚠️ Currently in TEST MODE - No real trades will be executed

🕐 Bot Status: ✅ Online
📅 Last Update: {current_time}"""

        bot.edit_message_text(
            welcome_text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=create_main_menu(user.id),
        )
        return

    # Handle new gamification and premium callbacks
    elif data == "leaderboard":
        if not gamification_manager:
            text = "❌ Gamification system not available"
        else:
            lang = get_user_language(user.id)
            xp_leaderboard = gamification_manager.get_leaderboard("weekly", "xp", 10)
            profit_leaderboard = gamification_manager.get_leaderboard("weekly", "profit", 5)

            if lang == "nl":
                text = "🏆 **Leaderboard**\n\n⭐ **Top XP (Deze Week):**\n"
                for i, entry in enumerate(xp_leaderboard, 1):
                    medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                    text += f"{medal} {entry['username']}: {entry['value']:,} XP\n"

                text += "\n💰 **Top Winst (Deze Week):**\n"
                for i, entry in enumerate(profit_leaderboard, 1):
                    medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                    text += f"{medal} {entry['username']}: €{entry['value']:.2f}\n"
            else:
                text = "🏆 **Leaderboard**\n\n⭐ **Top XP (This Week):**\n"
                for i, entry in enumerate(xp_leaderboard, 1):
                    medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                    text += f"{medal} {entry['username']}: {entry['value']:,} XP\n"

                text += "\n💰 **Top Profit (This Week):**\n"
                for i, entry in enumerate(profit_leaderboard, 1):
                    medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                    text += f"{medal} {entry['username']}: €{entry['value']:.2f}\n"

    elif data == "premium_info":
        if not premium_manager:
            text = "❌ Premium system not available"
        else:
            lang = get_user_language(user.id)
            subscription_text = premium_manager.get_subscription_info_text(user.id, lang)

            # Create upgrade buttons
            markup = telebot.types.InlineKeyboardMarkup()

            if lang == "nl":
                btn1 = telebot.types.InlineKeyboardButton(
                    "💎 Upgrade naar Premium (€50/maand)", callback_data="upgrade_premium"
                )
                btn2 = telebot.types.InlineKeyboardButton(
                    "👑 Upgrade naar VIP (€150/maand)", callback_data="upgrade_vip"
                )
                btn3 = telebot.types.InlineKeyboardButton("🏠 Terug naar Hoofdmenu", callback_data="back_to_main")
            else:
                btn1 = telebot.types.InlineKeyboardButton(
                    "💎 Upgrade to Premium (€50/month)", callback_data="upgrade_premium"
                )
                btn2 = telebot.types.InlineKeyboardButton("👑 Upgrade to VIP (€150/month)", callback_data="upgrade_vip")
                btn3 = telebot.types.InlineKeyboardButton("🏠 Back to Main Menu", callback_data="back_to_main")

            markup.add(btn1)
            markup.add(btn2)
            markup.add(btn3)

            bot.edit_message_text(
                subscription_text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=markup,
                parse_mode="Markdown",
            )
            return

    elif data == "real_trading":
        if not real_trading_manager:
            text = "❌ Real trading system not available"
        else:
            lang = get_user_language(user.id)
            status_text = real_trading_manager.get_real_trading_status_text(user.id, lang)

            # Create real trading menu for authorized users
            if real_trading_manager.is_authorized_for_real_trading(user.id):
                markup = telebot.types.InlineKeyboardMarkup()

                if lang == "nl":
                    btn1 = telebot.types.InlineKeyboardButton("💰 Bekijk Saldo", callback_data="real_balance")
                    btn2 = telebot.types.InlineKeyboardButton(
                        "📊 Trading Performance", callback_data="real_performance"
                    )
                    btn3 = telebot.types.InlineKeyboardButton("🏠 Terug naar Hoofdmenu", callback_data="back_to_main")
                else:
                    btn1 = telebot.types.InlineKeyboardButton("💰 View Balance", callback_data="real_balance")
                    btn2 = telebot.types.InlineKeyboardButton(
                        "📊 Trading Performance", callback_data="real_performance"
                    )
                    btn3 = telebot.types.InlineKeyboardButton("🏠 Back to Main Menu", callback_data="back_to_main")

                markup.add(btn1, btn2)
                markup.add(btn3)

                bot.edit_message_text(
                    status_text,
                    call.message.chat.id,
                    call.message.message_id,
                    reply_markup=markup,
                    parse_mode="Markdown",
                )
                return
            else:
                text = status_text

    elif data == "upgrade_premium":
        if not premium_manager:
            text = "❌ Premium system not available"
        else:
            lang = get_user_language(user.id)
            if lang == "nl":
                text = """
💎 **Premium Upgrade**

⚠️ **Let op:** Dit is een demo systeem.
In de echte versie zou hier een betaallink komen.

📧 **Contact:** @InnovarsLabo voor premium toegang

🔄 **Demo Upgrade:** Tijdelijk premium toegang voor testing
"""
            else:
                text = """
💎 **Premium Upgrade**

⚠️ **Note:** This is a demo system.
In the real version there would be a payment link here.

📧 **Contact:** @InnovarsLabo for premium access

🔄 **Demo Upgrade:** Temporary premium access for testing
"""

    elif data == "market_analysis":
        text = f"""
📊 **Market Analysis Report**

🕐 **Analysis Time:** {datetime.now().strftime("%H:%M:%S")}

📈 **Market Overview:**
• **BTC/USDT:** $95,234 (+2.3% 24h)
• **ETH/USDT:** $3,456 (+1.8% 24h)
• **Market Cap:** $2.1T (+0.9% 24h)

🔍 **Technical Analysis:**
• **Trend:** Bullish momentum
• **Support:** $94,000 (BTC)
• **Resistance:** $97,500 (BTC)

⚠️ **Note:** Live data temporarily unavailable
Exchange connections being optimized...

🔄 **Refresh in 5 minutes for updated data**
"""

    elif data == "portfolio":
        text = f"""
💰 **Portfolio Overview**

👤 **User:** {user.username or user.first_name}
🕐 **Last Update:** {datetime.now().strftime("%H:%M:%S")}

💼 **Account Status:**
• **Mode:** TEST MODE
• **Total Value:** $0.00 (Demo)
• **Available:** $1,000.00 (Demo)

📊 **Holdings:**
• No active positions
• Ready for demo trading

⚙️ **Exchange Status:**
• KuCoin: 🔄 Connecting...
• MEXC: 🔄 Connecting...

📝 **Note:** Real trading disabled in test mode
"""

    elif data == "start_trading":
        text = """
🚀 **Trading Interface**

⚠️ **TEST MODE ACTIVE**

🎯 **Available Strategies:**
• Trend Following
• Mean Reversion
• Breakout Trading
• DCA Strategy

💡 **Demo Trading:**
• Practice with virtual funds
• Learn trading strategies
• No real money at risk

🔧 **Setup Required:**
• Exchange API configuration
• Risk management settings
• Strategy selection

📞 **Contact admin to enable live trading**
"""

    elif data == "settings":
        text = f"""
⚙️ **Bot Settings**

👤 **User Settings:**
• **User ID:** {user.id}
• **Username:** @{user.username or "Not set"}
• **Admin:** {"✅ Yes" if user.id == ADMIN_USER_ID else "❌ No"}

🔧 **Trading Settings:**
• **Mode:** TEST MODE
• **Risk Level:** Conservative
• **Max Position:** $100
• **Stop Loss:** 5%

🔔 **Notifications:**
• **Alerts:** ✅ Enabled
• **Reports:** ✅ Enabled
• **Errors:** ✅ Enabled

📊 **Exchange Settings:**
• **KuCoin:** 🔄 Configuring...
• **MEXC:** 🔄 Configuring...
"""

    elif data == "help":
        text = """
❓ **Help & Commands**

📚 **Available Commands:**
• `/start` - Show main menu
• `/help` - Show this help

🎮 **Button Functions:**
• **📊 Market Analysis** - View market data
• **💰 Portfolio** - Check your balance
• **🚀 START TRADING** - Trading interface
• **⚙️ Settings** - Configure bot
• **📈 Live Prices** - Real-time prices

🆘 **Support:**
• Contact: @InnovarsLabo
• Issues: Report via admin
• Updates: Check announcements

📖 **Documentation:**
• Trading guide available
• Risk management tips
• Strategy explanations
"""

    elif data == "live_prices":
        # Get real crypto prices
        symbols = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        price_text = f"""
📈 **Live Price Feed**

🕐 **Last Update:** {datetime.now().strftime("%H:%M:%S")}

💰 **Major Cryptocurrencies:**
"""

        for symbol in symbols:
            price_data = get_crypto_price(symbol)
            if price_data:
                price = format_price(price_data["price"])
                change = price_data["change_24h"]
                change_emoji = "📈" if change > 0 else "📉" if change < 0 else "➡️"
                price_text += f"• **{symbol}:** {price} ({change_emoji}{change:+.2f}%)\n"
            else:
                price_text += f"• **{symbol}:** Loading...\n"

        price_text += f"""
📊 **Exchange Status:**
• **KuCoin:** {"🟢 Connected" if "kucoin" in EXCHANGES else "🔴 Disconnected"}
• **MEXC:** {"🟢 Connected" if "mexc" in EXCHANGES else "🔴 Disconnected"}

🛒 **Ready to trade!** Use 'Buy Crypto' to purchase.
"""
        text = price_text

    elif data == "portfolio":
        # Get real portfolio data
        balance_data = get_account_balance()
        if balance_data:
            total_usd = balance_data.get("USDT", {}).get("free", 0) or 0
            text = f"""
💰 **Portfolio Overview**

👤 **User:** {user.username or user.first_name}
🕐 **Last Update:** {datetime.now().strftime("%H:%M:%S")}

💼 **Account Balance:**
• **USDT:** ${total_usd:.2f}
• **Mode:** {"TEST MODE" if os.getenv("TEST_MODE", "true").lower() == "true" else "LIVE TRADING"}

📊 **Holdings:**
"""
            # Show crypto holdings
            for currency, data in balance_data.items():
                if currency != "USDT" and data.get("free", 0) > 0:
                    amount = data["free"]
                    text += f"• **{currency}:** {amount:.6f}\n"

            if not any(data.get("free", 0) > 0 for currency, data in balance_data.items() if currency != "USDT"):
                text += "• No crypto holdings yet\n"

            text += f"""
⚙️ **Exchange Status:**
• **KuCoin:** {"🟢 Connected" if "kucoin" in EXCHANGES else "🔴 Disconnected"}
• **MEXC:** {"🟢 Connected" if "mexc" in EXCHANGES else "🔴 Disconnected"}

🛒 **Ready to buy crypto!**
"""
        else:
            text = f"""
💰 **Portfolio Overview**

👤 **User:** {user.username or user.first_name}
🕐 **Last Update:** {datetime.now().strftime("%H:%M:%S")}

❌ **Unable to fetch portfolio data**
• Check exchange connections
• Verify API keys

⚙️ **Exchange Status:**
• **KuCoin:** {"🟢 Connected" if "kucoin" in EXCHANGES else "🔴 Disconnected"}
• **MEXC:** {"🟢 Connected" if "mexc" in EXCHANGES else "🔴 Disconnected"}
"""

    elif data == "buy_crypto":
        text = """
🛒 **Buy Cryptocurrency**

💡 **Choose a cryptocurrency to buy:**

⚠️ **TEST MODE ACTIVE**
• All orders are simulated
• No real money will be spent
• Perfect for learning!

🔒 **Safe & Secure:**
• Small amounts only
• Instant execution
• Real-time prices

👇 **Select crypto to buy:**
"""
        bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=create_buy_crypto_menu(),
            parse_mode="Markdown",
        )
        return

    elif data.startswith("buy_"):
        # Handle crypto selection for buying
        symbol = data.replace("buy_", "")
        price_data = get_crypto_price(symbol)

        if price_data:
            text = f"""
🛒 **Buy {symbol.split("/")[0]}**

💰 **Current Price:** {format_price(price_data["price"])}
📈 **24h Change:** {price_data["change_24h"]:+.2f}%
📊 **24h Volume:** {price_data["volume"]:,.0f}

💡 **Choose amount to buy:**

⚠️ **TEST MODE:** No real money will be spent
"""
            bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=create_buy_amount_menu(symbol),
                parse_mode="Markdown",
            )
            return
        else:
            text = f"❌ Unable to fetch price for {symbol}. Please try again."

    elif data.startswith("confirm_buy_"):
        # Handle buy confirmation
        parts = data.split("_")
        if len(parts) >= 5:
            symbol = parts[2] + "/" + parts[3]  # e.g., BTC/USDT
            usd_amount = float(parts[4])
        else:
            text = "❌ Invalid order format. Please try again."
            bot.edit_message_text(text, call.message.chat.id, call.message.message_id, reply_markup=create_back_menu())
            return

        # Execute the buy order
        price_data = get_crypto_price(symbol)
        if not price_data:
            text = "❌ Unable to get current price. Please try again later."
        else:
            order = place_buy_order(symbol, usd_amount / price_data["price"])

            if order:
                text = f"""
✅ **Order Executed Successfully!**

📋 **Order Details:**
• **Symbol:** {symbol}
• **Type:** Market Buy
• **Amount:** {order["amount"]:.6f} {symbol.split("/")[0]}
• **Price:** {format_price(order["price"])}
• **Total Cost:** ${order["cost"]:.2f}
• **Status:** {order["status"].title()}

🕐 **Time:** {datetime.now().strftime("%H:%M:%S")}
📝 **Order ID:** {order["id"]}

⚠️ **TEST MODE:** This was a simulated order
"""
            else:
                text = "❌ Order failed. Please try again or contact support."

    # ADMIN PANEL HANDLERS
    elif data == "admin_panel":
        if not check_user_permissions(user.id, "admin_panel"):
            text = "❌ **Access Denied**\n\nYou don't have admin permissions."
        else:
            user_data = user_manager.get_user(user.id)
            stats = user_manager.get_stats()

            text = f"""
🔧 **Admin Panel**

👤 **Admin:** {user.first_name}
🎭 **Role:** {user_data["role"].title()}

📊 **System Statistics:**
• **Total Users:** {stats["total_users"]}
• **Active Users (24h):** {stats["active_users"]}
• **Admins:** {stats["role_distribution"].get("admin", 0) + stats["role_distribution"].get("super_admin", 0)}

🔧 **Available Functions:**
• User Management
• Role Assignment
• System Monitoring
• Activity Logs

⚡ **System Status:** 🟢 Online
"""
            bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=create_admin_menu(),
                parse_mode="Markdown",
            )
            return

    elif data == "admin_manage_users":
        if not check_user_permissions(user.id, "manage_users"):
            text = "❌ **Access Denied**\n\nYou don't have user management permissions."
        else:
            users = user_manager.get_all_users()
            text = f"""
👥 **User Management**

📊 **Total Users:** {len(users)}

🔧 **Management Options:**
• View all users
• Search specific user
• Edit user permissions
• Ban/Unban users
• Set trading limits

👇 **Choose an action:**
"""
            bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=create_user_management_menu(),
                parse_mode="Markdown",
            )
            return

    elif data == "admin_view_stats":
        if not check_user_permissions(user.id, "view_stats"):
            text = "❌ **Access Denied**"
        else:
            stats = user_manager.get_stats()
            users = user_manager.get_all_users()

            text = f"""
📊 **System Statistics**

👥 **User Statistics:**
• **Total Users:** {stats["total_users"]}
• **Active (24h):** {stats["active_users"]}
• **New Today:** {len([u for u in users if u["created_at"] and "today" in u["created_at"]])}

🎭 **Role Distribution:**
"""
            for role, count in stats["role_distribution"].items():
                text += f"• **{role.title()}:** {count}\n"

            text += f"""
⚡ **System Health:**
• **Bot Status:** 🟢 Online
• **Exchange Status:** {"🟢" if EXCHANGES else "🔴"} Connected
• **Database:** 🟢 Operational

🕐 **Last Updated:** {datetime.now().strftime("%H:%M:%S")}
"""

    elif data == "admin_list_users":
        if not check_user_permissions(user.id, "manage_users"):
            text = "❌ **Access Denied**"
        else:
            users = user_manager.get_all_users()[:10]  # Show first 10 users
            text = """
📋 **User List** (Showing first 10)

"""
            for i, user_data in enumerate(users, 1):
                status = "🟢" if user_data["is_active"] else "🔴"
                text += f"{i}. {status} **{user_data['username'] or 'No username'}**\n"
                text += f"   ID: `{user_data['telegram_id']}` | Role: {user_data['role']}\n\n"

            text += f"📊 **Total Users:** {len(user_manager.get_all_users())}"

    else:
        text = "🤖 Unknown command. Use /start to see available options."

    # Edit message with new text and back button
    bot.edit_message_text(
        text, call.message.chat.id, call.message.message_id, reply_markup=create_back_menu(), parse_mode="Markdown"
    )


def main():
    """Start the bot"""
    print("🚀 Starting Simple Telegram Bot...")
    print("🤖 Bot: @mynewmoneymakersbot")
    print(f"👤 Admin: {ADMIN_USER_ID}")
    print("📱 Send /start to begin...")
    print("✅ Bot is now running!")

    # Start polling
    bot.polling(none_stop=True)


if __name__ == "__main__":
    main()
