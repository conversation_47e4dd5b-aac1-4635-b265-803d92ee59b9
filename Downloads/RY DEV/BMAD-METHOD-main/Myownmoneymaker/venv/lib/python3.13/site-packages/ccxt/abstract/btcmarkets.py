from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_markets = publicGetMarkets = Entry('markets', 'public', 'GET', {})
    public_get_markets_marketid_ticker = publicGetMarketsMarketIdTicker = Entry('markets/{marketId}/ticker', 'public', 'GET', {})
    public_get_markets_marketid_trades = publicGetMarketsMarketIdTrades = Entry('markets/{marketId}/trades', 'public', 'GET', {})
    public_get_markets_marketid_orderbook = publicGetMarketsMarketIdOrderbook = Entry('markets/{marketId}/orderbook', 'public', 'GET', {})
    public_get_markets_marketid_candles = publicGetMarketsMarketIdCandles = Entry('markets/{marketId}/candles', 'public', 'GET', {})
    public_get_markets_tickers = publicGetMarketsTickers = Entry('markets/tickers', 'public', 'GET', {})
    public_get_markets_orderbooks = publicGetMarketsOrderbooks = Entry('markets/orderbooks', 'public', 'GET', {})
    public_get_time = publicGetTime = Entry('time', 'public', 'GET', {})
    private_get_orders = privateGetOrders = Entry('orders', 'private', 'GET', {})
    private_get_orders_id = privateGetOrdersId = Entry('orders/{id}', 'private', 'GET', {})
    private_get_batchorders_ids = privateGetBatchordersIds = Entry('batchorders/{ids}', 'private', 'GET', {})
    private_get_trades = privateGetTrades = Entry('trades', 'private', 'GET', {})
    private_get_trades_id = privateGetTradesId = Entry('trades/{id}', 'private', 'GET', {})
    private_get_withdrawals = privateGetWithdrawals = Entry('withdrawals', 'private', 'GET', {})
    private_get_withdrawals_id = privateGetWithdrawalsId = Entry('withdrawals/{id}', 'private', 'GET', {})
    private_get_deposits = privateGetDeposits = Entry('deposits', 'private', 'GET', {})
    private_get_deposits_id = privateGetDepositsId = Entry('deposits/{id}', 'private', 'GET', {})
    private_get_transfers = privateGetTransfers = Entry('transfers', 'private', 'GET', {})
    private_get_transfers_id = privateGetTransfersId = Entry('transfers/{id}', 'private', 'GET', {})
    private_get_addresses = privateGetAddresses = Entry('addresses', 'private', 'GET', {})
    private_get_withdrawal_fees = privateGetWithdrawalFees = Entry('withdrawal-fees', 'private', 'GET', {})
    private_get_assets = privateGetAssets = Entry('assets', 'private', 'GET', {})
    private_get_accounts_me_trading_fees = privateGetAccountsMeTradingFees = Entry('accounts/me/trading-fees', 'private', 'GET', {})
    private_get_accounts_me_withdrawal_limits = privateGetAccountsMeWithdrawalLimits = Entry('accounts/me/withdrawal-limits', 'private', 'GET', {})
    private_get_accounts_me_balances = privateGetAccountsMeBalances = Entry('accounts/me/balances', 'private', 'GET', {})
    private_get_accounts_me_transactions = privateGetAccountsMeTransactions = Entry('accounts/me/transactions', 'private', 'GET', {})
    private_get_reports_id = privateGetReportsId = Entry('reports/{id}', 'private', 'GET', {})
    private_post_orders = privatePostOrders = Entry('orders', 'private', 'POST', {})
    private_post_batchorders = privatePostBatchorders = Entry('batchorders', 'private', 'POST', {})
    private_post_withdrawals = privatePostWithdrawals = Entry('withdrawals', 'private', 'POST', {})
    private_post_reports = privatePostReports = Entry('reports', 'private', 'POST', {})
    private_delete_orders = privateDeleteOrders = Entry('orders', 'private', 'DELETE', {})
    private_delete_orders_id = privateDeleteOrdersId = Entry('orders/{id}', 'private', 'DELETE', {})
    private_delete_batchorders_ids = privateDeleteBatchordersIds = Entry('batchorders/{ids}', 'private', 'DELETE', {})
    private_put_orders_id = privatePutOrdersId = Entry('orders/{id}', 'private', 'PUT', {})
