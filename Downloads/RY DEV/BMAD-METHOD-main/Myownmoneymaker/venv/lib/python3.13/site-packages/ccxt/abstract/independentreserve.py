from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_getvalidprimarycurrencycodes = publicGetGetValidPrimaryCurrencyCodes = Entry('GetValidPrimaryCurrencyCodes', 'public', 'GET', {})
    public_get_getvalidsecondarycurrencycodes = publicGetGetValidSecondaryCurrencyCodes = Entry('GetValidSecondaryCurrencyCodes', 'public', 'GET', {})
    public_get_getvalidlimitordertypes = publicGetGetValidLimitOrderTypes = Entry('GetValidLimitOrderTypes', 'public', 'GET', {})
    public_get_getvalidmarketordertypes = publicGetGetValidMarketOrderTypes = Entry('GetValidMarketOrderTypes', 'public', 'GET', {})
    public_get_getvalidordertypes = publicGetGetValidOrderTypes = Entry('GetValidOrderTypes', 'public', 'GET', {})
    public_get_getvalidtransactiontypes = publicGetGetValidTransactionTypes = Entry('GetValidTransactionTypes', 'public', 'GET', {})
    public_get_getmarketsummary = publicGetGetMarketSummary = Entry('GetMarketSummary', 'public', 'GET', {})
    public_get_getorderbook = publicGetGetOrderBook = Entry('GetOrderBook', 'public', 'GET', {})
    public_get_getallorders = publicGetGetAllOrders = Entry('GetAllOrders', 'public', 'GET', {})
    public_get_gettradehistorysummary = publicGetGetTradeHistorySummary = Entry('GetTradeHistorySummary', 'public', 'GET', {})
    public_get_getrecenttrades = publicGetGetRecentTrades = Entry('GetRecentTrades', 'public', 'GET', {})
    public_get_getfxrates = publicGetGetFxRates = Entry('GetFxRates', 'public', 'GET', {})
    public_get_getorderminimumvolumes = publicGetGetOrderMinimumVolumes = Entry('GetOrderMinimumVolumes', 'public', 'GET', {})
    public_get_getcryptowithdrawalfees = publicGetGetCryptoWithdrawalFees = Entry('GetCryptoWithdrawalFees', 'public', 'GET', {})
    private_post_getopenorders = privatePostGetOpenOrders = Entry('GetOpenOrders', 'private', 'POST', {})
    private_post_getclosedorders = privatePostGetClosedOrders = Entry('GetClosedOrders', 'private', 'POST', {})
    private_post_getclosedfilledorders = privatePostGetClosedFilledOrders = Entry('GetClosedFilledOrders', 'private', 'POST', {})
    private_post_getorderdetails = privatePostGetOrderDetails = Entry('GetOrderDetails', 'private', 'POST', {})
    private_post_getaccounts = privatePostGetAccounts = Entry('GetAccounts', 'private', 'POST', {})
    private_post_gettransactions = privatePostGetTransactions = Entry('GetTransactions', 'private', 'POST', {})
    private_post_getfiatbankaccounts = privatePostGetFiatBankAccounts = Entry('GetFiatBankAccounts', 'private', 'POST', {})
    private_post_getdigitalcurrencydepositaddress = privatePostGetDigitalCurrencyDepositAddress = Entry('GetDigitalCurrencyDepositAddress', 'private', 'POST', {})
    private_post_getdigitalcurrencydepositaddresses = privatePostGetDigitalCurrencyDepositAddresses = Entry('GetDigitalCurrencyDepositAddresses', 'private', 'POST', {})
    private_post_gettrades = privatePostGetTrades = Entry('GetTrades', 'private', 'POST', {})
    private_post_getbrokeragefees = privatePostGetBrokerageFees = Entry('GetBrokerageFees', 'private', 'POST', {})
    private_post_getdigitalcurrencywithdrawal = privatePostGetDigitalCurrencyWithdrawal = Entry('GetDigitalCurrencyWithdrawal', 'private', 'POST', {})
    private_post_placelimitorder = privatePostPlaceLimitOrder = Entry('PlaceLimitOrder', 'private', 'POST', {})
    private_post_placemarketorder = privatePostPlaceMarketOrder = Entry('PlaceMarketOrder', 'private', 'POST', {})
    private_post_cancelorder = privatePostCancelOrder = Entry('CancelOrder', 'private', 'POST', {})
    private_post_synchdigitalcurrencydepositaddresswithblockchain = privatePostSynchDigitalCurrencyDepositAddressWithBlockchain = Entry('SynchDigitalCurrencyDepositAddressWithBlockchain', 'private', 'POST', {})
    private_post_requestfiatwithdrawal = privatePostRequestFiatWithdrawal = Entry('RequestFiatWithdrawal', 'private', 'POST', {})
    private_post_withdrawfiatcurrency = privatePostWithdrawFiatCurrency = Entry('WithdrawFiatCurrency', 'private', 'POST', {})
    private_post_withdrawdigitalcurrency = privatePostWithdrawDigitalCurrency = Entry('WithdrawDigitalCurrency', 'private', 'POST', {})
