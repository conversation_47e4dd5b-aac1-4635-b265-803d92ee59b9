from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_assetpairs = publicGetAssetpairs = Entry('assetpairs', 'public', 'GET', {'cost': 2.5})
    public_get_assetpairs_id = publicGetAssetpairsId = Entry('assetpairs/{id}', 'public', 'GET', {'cost': 2.5})
    public_get_assets = publicGetAssets = Entry('assets', 'public', 'GET', {'cost': 2.5})
    public_get_assets_id = publicGetAssetsId = Entry('assets/{id}', 'public', 'GET', {'cost': 2.5})
    public_get_isalive = publicGetIsalive = Entry('isalive', 'public', 'GET', {'cost': 2.5})
    public_get_orderbooks = publicGetOrderbooks = Entry('orderbooks', 'public', 'GET', {'cost': 2.5})
    public_get_tickers = publicGetTickers = Entry('tickers', 'public', 'GET', {'cost': 2.5})
    public_get_prices = publicGetPrices = Entry('prices', 'public', 'GET', {'cost': 2.5})
    public_get_trades_public_assetpairid = publicGetTradesPublicAssetPairId = Entry('trades/public/{assetPairId}', 'public', 'GET', {'cost': 2.5})
    private_get_balance = privateGetBalance = Entry('balance', 'private', 'GET', {'cost': 2.5})
    private_get_trades = privateGetTrades = Entry('trades', 'private', 'GET', {'cost': 2.5})
    private_get_trades_order_orderid = privateGetTradesOrderOrderId = Entry('trades/order/{orderId}', 'private', 'GET', {'cost': 2.5})
    private_get_orders_active = privateGetOrdersActive = Entry('orders/active', 'private', 'GET', {'cost': 1})
    private_get_orders_closed = privateGetOrdersClosed = Entry('orders/closed', 'private', 'GET', {'cost': 1})
    private_get_orders_orderid = privateGetOrdersOrderId = Entry('orders/{orderId}', 'private', 'GET', {'cost': 1})
    private_get_operations = privateGetOperations = Entry('operations', 'private', 'GET', {'cost': 2.5})
    private_get_operations_deposits_addresses = privateGetOperationsDepositsAddresses = Entry('operations/deposits/addresses', 'private', 'GET', {'cost': 2.5})
    private_get_operations_deposits_addresses_assetid = privateGetOperationsDepositsAddressesAssetId = Entry('operations/deposits/addresses/{assetId}', 'private', 'GET', {'cost': 2.5})
    private_post_orders_limit = privatePostOrdersLimit = Entry('orders/limit', 'private', 'POST', {'cost': 1})
    private_post_orders_market = privatePostOrdersMarket = Entry('orders/market', 'private', 'POST', {'cost': 1})
    private_post_orders_bulk = privatePostOrdersBulk = Entry('orders/bulk', 'private', 'POST', {'cost': 1})
    private_post_operations_withdrawals = privatePostOperationsWithdrawals = Entry('operations/withdrawals', 'private', 'POST', {'cost': 2.5})
    private_post_operations_deposits_addresses = privatePostOperationsDepositsAddresses = Entry('operations/deposits/addresses', 'private', 'POST', {'cost': 2.5})
    private_delete_orders = privateDeleteOrders = Entry('orders', 'private', 'DELETE', {'cost': 1})
    private_delete_orders_orderid = privateDeleteOrdersOrderId = Entry('orders/{orderId}', 'private', 'DELETE', {'cost': 1})
