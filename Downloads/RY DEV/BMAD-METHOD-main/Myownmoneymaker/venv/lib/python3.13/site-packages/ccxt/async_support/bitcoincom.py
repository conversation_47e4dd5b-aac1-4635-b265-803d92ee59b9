# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.fmfwio import fmfwio
from ccxt.abstract.bitcoincom import ImplicitAPI


class bitcoincom(fmfwio, ImplicitAPI):

    def describe(self):
        return self.deep_extend(super(bitcoincom, self).describe(), {
            'id': 'bitcoincom',
            'name': 'Bitcoin.com',
            'alias': True,
        })
