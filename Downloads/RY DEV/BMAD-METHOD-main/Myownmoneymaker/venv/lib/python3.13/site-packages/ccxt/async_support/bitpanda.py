# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.onetrading import onetrading
from ccxt.abstract.bitpanda import ImplicitAPI


class bitpanda(onetrading, ImplicitAPI):

    def describe(self):
        return self.deep_extend(super(bitpanda, self).describe(), {
            'id': 'bitpanda',
            'alias': True,
        })
