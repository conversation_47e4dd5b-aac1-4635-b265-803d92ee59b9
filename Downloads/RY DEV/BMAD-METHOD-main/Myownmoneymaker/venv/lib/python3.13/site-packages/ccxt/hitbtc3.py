# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.hitbtc import hitbtc
from ccxt.abstract.hitbtc3 import ImplicitAPI


class hitbtc3(hitbtc, ImplicitAPI):

    def describe(self):
        return self.deep_extend(super(hitbtc3, self).describe(), {
            'id': 'hitbtc3',
            'alias': True,
        })
