# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.pro.binance import binance

import ccxt.async_support.binanceus as binanceusRest


class binanceus(binance):

    def describe(self):
        # eslint-disable-next-line new-cap
        restInstance = binanceusRest()
        restDescribe = restInstance.describe()
        extended = self.deep_extend(super(binanceus, self).describe(), restDescribe)
        return self.deep_extend(extended, {
            'id': 'binanceus',
            'name': 'Binance US',
            'countries': ['US'],  # US
            'certified': False,
            'urls': {
                'logo': 'https://user-images.githubusercontent.com/1294454/65177307-217b7c80-da5f-11e9-876e-0b748ba0a358.jpg',
                'api': {
                    'ws': {
                        'spot': 'wss://stream.binance.us:9443/ws',
                    },
                    'web': 'https://www.binance.us',
                    'sapi': 'https://api.binance.us/sapi/v1',
                    'wapi': 'https://api.binance.us/wapi/v3',
                    'public': 'https://api.binance.us/api/v1',
                    'private': 'https://api.binance.us/api/v3',
                    'v3': 'https://api.binance.us/api/v3',
                    'v1': 'https://api.binance.us/api/v1',
                },
                'www': 'https://www.binance.us',
                'referral': 'https://www.binance.us/?ref=35005074',
                'doc': 'https://github.com/binance-us/binance-official-api-docs',
                'fees': 'https://www.binance.us/en/fee/schedule',
            },
            'options': {
                'fetchCurrencies': False,
                'quoteOrderQty': False,
                'defaultType': 'spot',
                'fetchMarkets': ['spot'],
            },
            'fees': {
                'trading': {
                    'tierBased': False,
                    'percentage': True,
                    'taker': 0.0,  # 0.1% trading fee, zero fees for all trading pairs before November 1
                    'maker': 0.0,  # 0.1% trading fee, zero fees for all trading pairs before November 1
                },
            },
        })
