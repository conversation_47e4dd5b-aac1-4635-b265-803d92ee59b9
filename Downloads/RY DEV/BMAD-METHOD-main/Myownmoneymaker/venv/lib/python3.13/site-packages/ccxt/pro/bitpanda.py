# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.pro.onetrading import onetrading


class bitpanda(onetrading):

    def describe(self):
        return self.deep_extend(super(bitpanda, self).describe(), {
            'alias': True,
            'id': 'bitpanda',
        })
