import os
import sys

root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(root)

# ----------------------------------------------------------------------------

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

# ----------------------------------------------------------------------------
# -*- coding: utf-8 -*-


from ccxt.test.base import test_shared_methods  # noqa E402


def test_ledger_item(exchange, method, entry, requested_code, now):
    format = {
        'info': {},
        'id': 'x1234',
        'currency': 'BTC',
        'account': 'spot',
        'referenceId': '',
        'referenceAccount': '',
        'status': 'ok',
        'amount': exchange.parse_number('22'),
        'before': exchange.parse_number('111'),
        'after': exchange.parse_number('133'),
        'fee': {},
        'direction': 'in',
        'timestamp': *************,
        'datetime': '2021-11-30T00:00:00.000Z',
        'type': 'deposit',
    }
    empty_not_allowed_for = ['id', 'currency', 'account', 'status', 'direction']
    test_shared_methods.assert_structure(exchange, method, entry, format, empty_not_allowed_for)
    test_shared_methods.assert_timestamp(exchange, method, entry, now)
    test_shared_methods.assert_currency_code(exchange, method, entry, entry['currency'], requested_code)
    #
    test_shared_methods.assert_in_array(exchange, method, entry, 'direction', ['in', 'out'])
    # testSharedMethods.assertInArray (exchange, method, entry, 'type', ['trade', 'transaction', 'margin', 'cashback', 'referral', 'transfer', 'fee',  ]);
    # testSharedMethods.assertInArray (exchange, method, entry, 'account', ['spot', 'swap', .. ]);
    test_shared_methods.assert_greater_or_equal(exchange, method, entry, 'amount', '0')
    test_shared_methods.assert_greater_or_equal(exchange, method, entry, 'before', '0')
    test_shared_methods.assert_greater_or_equal(exchange, method, entry, 'after', '0')
    test_shared_methods.assert_fee(exchange, method, entry['fee'])
