-- ===================================
-- CRYPTO BOT USER MANAGEMENT DATABASE
-- ===================================

-- Users tabel - Hoof<PERSON><PERSON><PERSON> voor alle gebruikers
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(50),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP
);

-- Roles tabel - Definiëert alle beschikbare rollen
CREATE TABLE IF NOT EXISTS roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100),
    description TEXT,
    permissions TEXT, -- JSON string met permissions
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- User Sessions tabel - Voor session management
CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    session_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Audit Logs tabel - Voor het bijhouden van alle acties
CREATE TABLE IF NOT EXISTS audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource VARCHAR(100),
    resource_id VARCHAR(100),
    details TEXT, -- JSON string met extra details
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Trading Activities tabel - Voor het loggen van trading acties
CREATE TABLE IF NOT EXISTS trading_activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action_type VARCHAR(50), -- 'buy', 'sell', 'view_portfolio', etc.
    crypto_symbol VARCHAR(10),
    amount DECIMAL(18, 8),
    price DECIMAL(18, 8),
    exchange VARCHAR(50),
    order_id VARCHAR(100),
    status VARCHAR(20), -- 'pending', 'completed', 'failed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- ===================================
-- STANDAARD ROLLEN INVOEGEN
-- ===================================

INSERT OR IGNORE INTO roles (name, display_name, description, permissions) VALUES 
('superadmin', 'Super Administrator', 'Volledige controle over het systeem', 
 '["all_permissions", "manage_users", "manage_admins", "view_audit_logs", "manage_system", "ceo_dashboard", "trading_management"]'),

('admin', 'Administrator', 'Beheert bot instellingen en trading activiteiten', 
 '["manage_bot_settings", "view_trading_data", "manage_trading", "view_analytics", "moderate_users"]'),

('ceo', 'CEO Dashboard User', 'Alleen-lezen toegang tot CEO Dashboard', 
 '["view_ceo_dashboard", "view_analytics", "view_reports"]'),

('user', 'Regular User', 'Standaard gebruiker met basis trading rechten', 
 '["view_portfolio", "basic_trading", "view_prices"]'),

('viewer', 'Viewer', 'Alleen kijken, geen trading', 
 '["view_prices", "view_public_data"]');

-- ===================================
-- INDEXEN VOOR PERFORMANCE
-- ===================================

CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_trading_activities_user_id ON trading_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_trading_activities_created_at ON trading_activities(created_at);

-- ===================================
-- TRIGGERS VOOR AUTO-UPDATE
-- ===================================

-- Trigger om updated_at automatisch bij te werken
CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
