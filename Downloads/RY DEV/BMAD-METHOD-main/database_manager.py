import sqlite3
import json
from datetime import datetime
import logging

class DatabaseManager:
    def __init__(self, db_path="crypto_bot.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialiseer de database met alle benodigde tabellen"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users tabel
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                telegram_id BIGINT UNIQUE NOT NULL,
                username VARCHA<PERSON>(50),
                first_name VA<PERSON><PERSON><PERSON>(100),
                last_name <PERSON><PERSON><PERSON><PERSON>(100),
                role VARCHAR(20) DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT true,
                last_login TIMESTAMP
            )
        ''')
        
        # Roles tabel
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
                display_name VARCHAR(100),
                description TEXT,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT true
            )
        ''')
        
        # Audit logs tabel
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(100) NOT NULL,
                resource VARCHAR(100),
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        ''')
        
        # Standaard rollen invoegen
        self._insert_default_roles(cursor)
        
        conn.commit()
        conn.close()
        logging.info("Database geïnitialiseerd")
    
    def _insert_default_roles(self, cursor):
        """Voeg standaard rollen toe"""
        roles = [
            ('superadmin', 'Super Administrator', 'Volledige controle', 
             '["all_permissions", "manage_users", "manage_admins", "ceo_dashboard"]'),
            ('admin', 'Administrator', 'Bot beheer en trading', 
             '["manage_bot", "view_trading", "manage_trading"]'),
            ('ceo', 'CEO Dashboard', 'CEO dashboard toegang', 
             '["view_ceo_dashboard", "view_analytics"]'),
            ('user', 'Regular User', 'Basis trading rechten', 
             '["view_portfolio", "basic_trading"]')
        ]
        
        for role in roles:
            cursor.execute('''
                INSERT OR IGNORE INTO roles (name, display_name, description, permissions) 
                VALUES (?, ?, ?, ?)
            ''', role)
    
    def add_user(self, telegram_id, username=None, first_name=None, last_name=None, role='user'):
        """Voeg nieuwe gebruiker toe"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO users 
                (telegram_id, username, first_name, last_name, role, last_login) 
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (telegram_id, username, first_name, last_name, role, datetime.now()))
            
            conn.commit()
            user_id = cursor.lastrowid
            
            # Log de actie
            self.log_action(user_id, "user_added", "users", f"User {username} toegevoegd")
            
            conn.close()
            return True
        except Exception as e:
            logging.error(f"Fout bij toevoegen gebruiker: {e}")
            conn.close()
            return False
    
    def get_user(self, telegram_id):
        """Haal gebruiker op"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users WHERE telegram_id = ?', (telegram_id,))
        user = cursor.fetchone()
        
        conn.close()
        return user
    
    def update_user_role(self, telegram_id, new_role):
        """Update gebruiker rol"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE users SET role = ?, updated_at = ? 
                WHERE telegram_id = ?
            ''', (new_role, datetime.now(), telegram_id))
            
            conn.commit()
            
            # Log de actie
            user = self.get_user(telegram_id)
            if user:
                self.log_action(user[0], "role_updated", "users", f"Rol gewijzigd naar {new_role}")
            
            conn.close()
            return True
        except Exception as e:
            logging.error(f"Fout bij updaten rol: {e}")
            conn.close()
            return False
    
    def get_user_permissions(self, telegram_id):
        """Haal gebruiker permissies op"""
        user = self.get_user(telegram_id)
        if not user:
            return []
        
        role = user[5]  # role kolom
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT permissions FROM roles WHERE name = ?', (role,))
        result = cursor.fetchone()
        
        conn.close()
        
        if result and result[0]:
            try:
                return json.loads(result[0])
            except:
                return []
        return []
    
    def has_permission(self, telegram_id, permission):
        """Check of gebruiker specifieke permissie heeft"""
        permissions = self.get_user_permissions(telegram_id)
        return "all_permissions" in permissions or permission in permissions
    
    def get_all_users(self):
        """Haal alle gebruikers op"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
        users = cursor.fetchall()
        
        conn.close()
        return users
    
    def log_action(self, user_id, action, resource, details):
        """Log een actie in audit logs"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO audit_logs (user_id, action, resource, details) 
            VALUES (?, ?, ?, ?)
        ''', (user_id, action, resource, details))
        
        conn.commit()
        conn.close()
    
    def get_audit_logs(self, limit=50):
        """Haal audit logs op"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT al.*, u.username, u.first_name 
            FROM audit_logs al 
            LEFT JOIN users u ON al.user_id = u.id 
            ORDER BY al.created_at DESC 
            LIMIT ?
        ''', (limit,))
        
        logs = cursor.fetchall()
        conn.close()
        return logs
