# BMad Daily Digest Documentation

Welcome to the BMad Daily Digest documentation index. This page provides links to all project documentation.

## Project Overview & Requirements

- [Product Requirements Document (PRD)](./prd.md)
- [UI/UX Specification](./ux-ui-spec.md)
- [Architecture Overview](./architecture.md)

## Epics & User Stories

- [Epic 1: Backend Foundation](./epic-1.md) - Backend project setup and "Hello World" API
- [Epic 2: Content Ingestion & Podcast Generation](./epic-2.md) - Core data pipeline and podcast creation
- [Epic 3: Web Application & Podcast Consumption](./epic-3.md) - Frontend implementation for end-users

## Technical Documentation

### Backend Architecture

- [API Reference](./api-reference.md) - External and internal API documentation
- [Data Models](./data-models.md) - Core data entities and schemas
- [Component View](./component-view.md) - System components and their interactions
- [Sequence Diagrams](./sequence-diagrams.md) - Core workflows and processes
- [Project Structure](./project-structure.md) - Repository organization
- [Environment Variables](./environment-vars.md) - Configuration settings

### Infrastructure & Operations

- [Technology Stack](./tech-stack.md) - Definitive technology selections
- [Infrastructure and Deployment](./infra-deployment.md) - Deployment architecture
- [Operational Guidelines](./operational-guidelines.md) - Coding standards, testing, error handling, and security

## Reference Materials

- [Key References](./key-references.md) - External documentation and resources
- [Change Log](./change-log.md) - Document version history 