# Documentation Index

## Overview

This index catalogs all documentation files for the BMAD-METHOD project, organized by category for easy reference and AI discoverability.

## Product Documentation

- **[prd.md](prd.md)** - Product Requirements Document outlining the core project scope, features and business objectives.
- **[final-brief-with-pm-prompt.md](final-brief-with-pm-prompt.md)** - Finalized project brief with Product Management specifications.
- **[demo.md](demo.md)** - Main demonstration guide for the BMAD-METHOD project.

## Architecture & Technical Design

- **[architecture.md](architecture.md)** - System architecture documentation detailing technical components and their interactions.
- **[tech-stack.md](tech-stack.md)** - Overview of the technology stack used in the project.
- **[project-structure.md](project-structure.md)** - Explanation of the project's file and folder organization.
- **[data-models.md](data-models.md)** - Documentation of data models and database schema.
- **[environment-vars.md](environment-vars.md)** - Required environment variables and configuration settings.

## API Documentation

- **[api-reference.md](api-reference.md)** - Comprehensive API endpoints and usage reference.

## Epics & User Stories

- **[epic1.md](epic1.md)** - Epic 1 definition and scope.
- **[epic2.md](epic2.md)** - Epic 2 definition and scope.
- **[epic3.md](epic3.md)** - Epic 3 definition and scope.
- **[epic4.md](epic4.md)** - Epic 4 definition and scope.
- **[epic5.md](epic5.md)** - Epic 5 definition and scope.
- **[epic-1-stories-demo.md](epic-1-stories-demo.md)** - Detailed user stories for Epic 1.
- **[epic-2-stories-demo.md](epic-2-stories-demo.md)** - Detailed user stories for Epic 2.
- **[epic-3-stories-demo.md](epic-3-stories-demo.md)** - Detailed user stories for Epic 3.

## Development Standards

- **[coding-standards.md](coding-standards.md)** - Coding conventions and standards for the project.
- **[testing-strategy.md](testing-strategy.md)** - Approach to testing, including methodologies and tools.

## AI & Prompts

- **[prompts.md](prompts.md)** - AI prompt templates and guidelines for project assistants.
- **[combined-artifacts-for-posm.md](combined-artifacts-for-posm.md)** - Consolidated project artifacts for Product Owner and Solution Manager.

## Reference Documents

- **[botched-architecture-draft.md](botched-architecture-draft.md)** - Archived architecture draft (for reference only).
