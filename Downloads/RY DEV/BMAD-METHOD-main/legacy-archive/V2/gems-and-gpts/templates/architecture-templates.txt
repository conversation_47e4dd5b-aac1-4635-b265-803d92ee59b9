# Architecture Sub Document Templates

## Master Architecture Template
```Markdown
# {Project Name} Architecture Document

## Technical Summary

{Provide a brief (1-2 paragraph) overview of the system's architecture, key components, technology choices, and architectural patterns used. Reference the goals from the PRD.}

## High-Level Overview

{Describe the main architectural style (e.g., Monolith, Microservices, Serverless, Event-Driven). Explain the primary user interaction or data flow at a conceptual level.}

```mermaid
{Insert high-level system context or interaction diagram here - e.g., using Mermaid graph TD or C4 Model Context Diagram}
```

## Component View

{Describe the major logical components or services of the system and their responsibilities. Explain how they collaborate.}

```mermaid
{Insert component diagram here - e.g., using Mermaid graph TD or C4 Model Container/Component Diagram}
```

- Component A: {Description of responsibility}
- Component B: {Description of responsibility}
- {src/ Directory (if applicable): The application code in src/ is organized into logical modules... (briefly describe key subdirectories like clients, core, services, etc., referencing docs/project-structure.md for the full layout)}

## Key Architectural Decisions & Patterns

{List significant architectural choices and the patterns employed.}

- Pattern/Decision 1: {e.g., Choice of Database, Message Queue Usage, Authentication Strategy, API Design Style (REST/GraphQL)} - Justification: {...}
- Pattern/Decision 2: {...} - Justification: {...}
- (See docs/coding-standards.md for detailed coding patterns and error handling)

## Core Workflow / Sequence Diagrams (Optional)

{Illustrate key or complex workflows using sequence diagrams if helpful.}

## Infrastructure and Deployment Overview

- Cloud Provider(s): {e.g., AWS, Azure, GCP, On-premise}
- Core Services Used: {List key managed services - e.g., Lambda, S3, Kubernetes Engine, RDS, Kafka}
- Infrastructure as Code (IaC): {Tool used - e.g., AWS CDK, Terraform, Pulumi, ARM Templates} - Location: {Link to IaC code repo/directory}
- Deployment Strategy: {e.g., CI/CD pipeline, Manual deployment steps, Blue/Green, Canary} - Tools: {e.g., Jenkins, GitHub Actions, GitLab CI}
- Environments: {List environments - e.g., Development, Staging, Production}
- (See docs/environment-vars.md for configuration details)

## Key Reference Documents

{Link to other relevant documents in the docs/ folder.}

- docs/prd.md
- docs/epicN.md files
- docs/tech-stack.md
- docs/project-structure.md
- docs/coding-standards.md
- docs/api-reference.md
- docs/data-models.md
- docs/environment-vars.md
- docs/testing-strategy.md
- docs/ui-ux-spec.md (if applicable)
- ... (other relevant docs)

## Change Log

| Change        | Date       | Version | Description                  | Author         |
| ------------- | ---------- | ------- | ---------------------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft based on brief | {Agent/Person} |
| ...           | ...        | ...     | ...                          | ...            |

```
## Coding Standards Template

```Markdown
# {Project Name} Coding Standards and Patterns

## Architectural / Design Patterns Adopted

{List the key high-level patterns chosen in the architecture document.}

- **Pattern 1:** {e.g., Serverless, Event-Driven, Microservices, CQRS} - _Rationale/Reference:_ {Briefly why, or link to `docs/architecture.md` section}
- **Pattern 2:** {e.g., Dependency Injection, Repository Pattern, Module Pattern} - _Rationale/Reference:_ {...}
- **Pattern N:** {...}

## Coding Standards (Consider adding these to Dev Agent Context or Rules)

- **Primary Language(s):** {e.g., TypeScript 5.x, Python 3.11, Go 1.2x}
- **Primary Runtime(s):** {e.g., Node.js 22.x, Python Runtime for Lambda}
- **Style Guide & Linter:** {e.g., ESLint with Airbnb config, Prettier; Black, Flake8; Go fmt} - _Configuration:_ {Link to config files or describe setup}
- **Naming Conventions:**
  - Variables: `{e.g., camelCase}`
  - Functions: `{e.g., camelCase}`
  - Classes/Types/Interfaces: `{e.g., PascalCase}`
  - Constants: `{e.g., UPPER_SNAKE_CASE}`
  - Files: `{e.g., kebab-case.ts, snake_case.py}`
- **File Structure:** Adhere to the layout defined in `docs/project-structure.md`.
- **Asynchronous Operations:** {e.g., Use `async`/`await` in TypeScript/Python, Goroutines/Channels in Go.}
- **Type Safety:** {e.g., Leverage TypeScript strict mode, Python type hints, Go static typing.} - _Type Definitions:_ {Location, e.g., `src/common/types.ts`}
- **Comments & Documentation:** {Expectations for code comments, docstrings, READMEs.}
- **Dependency Management:** {Tool used - e.g., npm, pip, Go modules. Policy on adding dependencies.}

## Error Handling Strategy

- **General Approach:** {e.g., Use exceptions, return error codes/tuples, specific error types.}
- **Logging:**
  - Library/Method: {e.g., `console.log/error`, Python `logging` module, dedicated logging library}
  - Format: {e.g., JSON, plain text}
  - Levels: {e.g., DEBUG, INFO, WARN, ERROR}
  - Context: {What contextual information should be included?}
- **Specific Handling Patterns:**
  - External API Calls: {e.g., Use `try/catch`, check response codes, implement retries with backoff for transient errors?}
  - Input Validation: {Where and how is input validated?}
  - Graceful Degradation vs. Critical Failure: {Define criteria for when to continue vs. halt.}

## Security Best Practices

{Outline key security considerations relevant to the codebase.}

- Input Sanitization/Validation: {...}
- Secrets Management: {How are secrets handled in code? Reference `docs/environment-vars.md` regarding storage.}
- Dependency Security: {Policy on checking for vulnerable dependencies.}
- Authentication/Authorization Checks: {Where should these be enforced?}
- {Other relevant practices...}

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |
```

## Data Models Template

```Markdown
# {Project Name} Data Models

## 2. Core Application Entities / Domain Objects

{Define the main objects/concepts the application works with. Repeat subsection for each key entity.}

### {Entity Name, e.g., User, Order, Product}

- **Description:** {What does this entity represent?}
- **Schema / Interface Definition:**
  ```typescript
  // Example using TypeScript Interface
  export interface {EntityName} {
    id: string; // {Description, e.g., Unique identifier}
    propertyName: string; // {Description}
    optionalProperty?: number; // {Description}
    // ... other properties
  }
  ```
  _(Alternatively, use JSON Schema, class definitions, or other relevant format)_
- **Validation Rules:** {List any specific validation rules beyond basic types - e.g., max length, format, range.}

### {Another Entity Name}

{...}

## API Payload Schemas (If distinct)

{Define schemas specifically for data sent to or received from APIs, if they differ significantly from the core entities. Reference `docs/api-reference.md`.}

### {API Endpoint / Purpose, e.g., Create Order Request}

- **Schema / Interface Definition:**
  ```typescript
  // Example
  export interface CreateOrderRequest {
    customerId: string;
    items: { productId: string; quantity: number }[];
    // ...
  }
  ```

### {Another API Payload}

{...}

## Database Schemas (If applicable)

{If using a database, define table structures or document database schemas.}

### {Table / Collection Name}

- **Purpose:** {What data does this table store?}
- **Schema Definition:**
  ```sql
  -- Example SQL
  CREATE TABLE {TableName} (
    id VARCHAR(36) PRIMARY KEY,
    column_name VARCHAR(255) NOT NULL,
    numeric_column DECIMAL(10, 2),
    -- ... other columns, indexes, constraints
  );
  ```
  _(Alternatively, use ORM model definitions, NoSQL document structure, etc.)_

### {Another Table / Collection Name}

{...}

## State File Schemas (If applicable)

{If the application uses files for persisting state.}

### {State File Name / Purpose, e.g., processed_items.json}

- **Purpose:** {What state does this file track?}
- **Format:** {e.g., JSON}
- **Schema Definition:**
  ```json
  {
    "type": "object",
    "properties": {
      "processedIds": {
        "type": "array",
        "items": {
          "type": "string"
        },
        "description": "List of IDs that have been processed."
      }
      // ... other state properties
    },
    "required": ["processedIds"]
  }
  ```

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |
```

## Environment Vars Templates

```Markdown
# {Project Name} Environment Variables

## Configuration Loading Mechanism

{Describe how environment variables are loaded into the application.}

- **Local Development:** {e.g., Using `.env` file with `dotenv` library.}
- **Deployment (e.g., AWS Lambda, Kubernetes):** {e.g., Set via Lambda function configuration, Kubernetes Secrets/ConfigMaps.}

## Required Variables

{List all environment variables used by the application.}

| Variable Name        | Description                                     | Example / Default Value               | Required? (Yes/No) | Sensitive? (Yes/No) |
| :------------------- | :---------------------------------------------- | :------------------------------------ | :----------------- | :------------------ |
| `NODE_ENV`           | Runtime environment                             | `development` / `production`          | Yes                | No                  |
| `PORT`               | Port the application listens on (if applicable) | `8080`                                | No                 | No                  |
| `DATABASE_URL`       | Connection string for the primary database      | `postgresql://user:pass@host:port/db` | Yes                | Yes                 |
| `EXTERNAL_API_KEY`   | API Key for {External Service Name}             | `sk_...`                              | Yes                | Yes                 |
| `S3_BUCKET_NAME`     | Name of the S3 bucket for {Purpose}             | `my-app-data-bucket-...`              | Yes                | No                  |
| `FEATURE_FLAG_X`     | Enables/disables experimental feature X         | `false`                               | No                 | No                  |
| `{ANOTHER_VARIABLE}` | {Description}                                   | {Example}                             | {Yes/No}           | {Yes/No}            |
| ...                  | ...                                             | ...                                   | ...                | ...                 |

## Notes

- **Secrets Management:** {Explain how sensitive variables (API Keys, passwords) should be handled, especially in production (e.g., "Use AWS Secrets Manager", "Inject via CI/CD pipeline").}
- **`.env.example`:** {Mention that an `.env.example` file should be maintained in the repository with placeholder values for developers.}
- **Validation:** {Is there code that validates the presence or format of these variables at startup?}

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |

```

## Project Structure Template Example

```Markdown
# {Project Name} Project Structure

{Provide an ASCII or Mermaid diagram representing the project's folder structure such as the following example.}

```plaintext
{project-root}/
├── .github/                    # CI/CD workflows (e.g., GitHub Actions)
│   └── workflows/
│       └── main.yml
├── .vscode/                    # VSCode settings (optional)
│   └── settings.json
├── build/                      # Compiled output (if applicable, often git-ignored)
├── config/                     # Static configuration files (if any)
├── docs/                       # Project documentation (PRD, Arch, etc.)
│   ├── index.md
│   └── ... (other .md files)
├── infra/                      # Infrastructure as Code (e.g., CDK, Terraform)
│   └── lib/
│   └── bin/
├── node_modules/               # Project dependencies (git-ignored)
├── scripts/                    # Utility scripts (build, deploy helpers, etc.)
├── src/                        # Application source code
│   ├── common/                 # Shared utilities, types, constants
│   ├── components/             # Reusable UI components (if UI exists)
│   ├── features/               # Feature-specific modules (alternative structure)
│   │   └── feature-a/
│   ├── core/                   # Core business logic
│   ├── clients/                # External API/Service clients
│   ├── services/               # Internal services / Cloud SDK wrappers
│   ├── pages/ / routes/        # UI pages or API route definitions
│   └── main.ts / index.ts / app.ts # Application entry point
├── stories/                    # Generated story files for development (optional)
│   └── epic1/
├── test/                       # Automated tests
│   ├── unit/                   # Unit tests (mirroring src structure)
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── .env.example                # Example environment variables
├── .gitignore                  # Git ignore rules
├── package.json                # Project manifest and dependencies
├── tsconfig.json               # TypeScript configuration (if applicable)
├── Dockerfile                  # Docker build instructions (if applicable)
└── README.md                   # Project overview and setup instructions
```

(Adjust the example tree based on the actual project type - e.g., Python would have requirements.txt, etc.)

## Key Directory Descriptions

docs/: Contains all project planning and reference documentation.
infra/: Holds the Infrastructure as Code definitions (e.g., AWS CDK, Terraform).
src/: Contains the main application source code.
common/: Code shared across multiple modules (utilities, types, constants). Avoid business logic here.
core/ / domain/: Core business logic, entities, use cases, independent of frameworks/external services.
clients/: Modules responsible for communicating with external APIs or services.
services/ / adapters/ / infrastructure/: Implementation details, interactions with databases, cloud SDKs, frameworks.
routes/ / controllers/ / pages/: Entry points for API requests or UI views.
test/: Contains all automated tests, mirroring the src/ structure where applicable.
scripts/: Helper scripts for build, deployment, database migrations, etc.

## Notes

{Mention any specific build output paths, compiler configuration pointers, or other relevant structural notes.}

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |
```

## Tech Stack Template

```Markdown
# {Project Name} Technology Stack

## Technology Choices

| Category             | Technology              | Version / Details | Description / Purpose                   | Justification (Optional) |
| :------------------- | :---------------------- | :---------------- | :-------------------------------------- | :----------------------- |
| **Languages**        | {e.g., TypeScript}      | {e.g., 5.x}       | {Primary language for backend/frontend} | {Why this language?}     |
|                      | {e.g., Python}          | {e.g., 3.11}      | {Used for data processing, ML}          | {...}                    |
| **Runtime**          | {e.g., Node.js}         | {e.g., 22.x}      | {Server-side execution environment}     | {...}                    |
| **Frameworks**       | {e.g., NestJS}          | {e.g., 10.x}      | {Backend API framework}                 | {Why this framework?}    |
|                      | {e.g., React}           | {e.g., 18.x}      | {Frontend UI library}                   | {...}                    |
| **Databases**        | {e.g., PostgreSQL}      | {e.g., 15}        | {Primary relational data store}         | {...}                    |
|                      | {e.g., Redis}           | {e.g., 7.x}       | {Caching, session storage}              | {...}                    |
| **Cloud Platform**   | {e.g., AWS}             | {N/A}             | {Primary cloud provider}                | {...}                    |
| **Cloud Services**   | {e.g., AWS Lambda}      | {N/A}             | {Serverless compute}                    | {...}                    |
|                      | {e.g., AWS S3}          | {N/A}             | {Object storage for assets/state}       | {...}                    |
|                      | {e.g., AWS EventBridge} | {N/A}             | {Event bus / scheduled tasks}           | {...}                    |
| **Infrastructure**   | {e.g., AWS CDK}         | {e.g., Latest}    | {Infrastructure as Code tool}           | {...}                    |
|                      | {e.g., Docker}          | {e.g., Latest}    | {Containerization}                      | {...}                    |
| **UI Libraries**     | {e.g., Material UI}     | {e.g., 5.x}       | {React component library}               | {...}                    |
| **State Management** | {e.g., Redux Toolkit}   | {e.g., Latest}    | {Frontend state management}             | {...}                    |
| **Testing**          | {e.g., Jest}            | {e.g., Latest}    | {Unit/Integration testing framework}    | {...}                    |
|                      | {e.g., Playwright}      | {e.g., Latest}    | {End-to-end testing framework}          | {...}                    |
| **CI/CD**            | {e.g., GitHub Actions}  | {N/A}             | {Continuous Integration/Deployment}     | {...}                    |
| **Other Tools**      | {e.g., LangChain.js}    | {e.g., Latest}    | {LLM interaction library}               | {...}                    |
|                      | {e.g., Cheerio}         | {e.g., Latest}    | {HTML parsing/scraping}                 | {...}                    |

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           |

```

## Testing Strategy Template

```Markdown
# {Project Name} Testing Strategy

## Overall Philosophy & Goals

{Describe the high-level approach. e.g., "Follow the Testing Pyramid/Trophy principle.", "Automate extensively.", "Focus on testing business logic and key integrations.", "Ensure tests run efficiently in CI/CD."}

- Goal 1: {e.g., Achieve X% code coverage for critical modules.}
- Goal 2: {e.g., Prevent regressions in core functionality.}
- Goal 3: {e.g., Enable confident refactoring.}

## Testing Levels

### Unit Tests

- **Scope:** Test individual functions, methods, or components in isolation. Focus on business logic, calculations, and conditional paths within a single module.
- **Tools:** {e.g., Jest, Pytest, Go testing package, JUnit, NUnit}
- **Mocking/Stubbing:** {How are dependencies mocked? e.g., Jest mocks, Mockito, Go interfaces}
- **Location:** {e.g., `test/unit/`, alongside source files (`*.test.ts`)}
- **Expectations:** {e.g., Should cover all significant logic paths. Fast execution.}

### Integration Tests

- **Scope:** Verify the interaction and collaboration between multiple internal components or modules. Test the flow of data and control within a specific feature or workflow slice. May involve mocking external APIs or databases, or using test containers.
- **Tools:** {e.g., Jest, Pytest, Go testing package, Testcontainers, Supertest (for APIs)}
- **Location:** {e.g., `test/integration/`}
- **Expectations:** {e.g., Focus on module boundaries and contracts. Slower than unit tests.}

### End-to-End (E2E) / Acceptance Tests

- **Scope:** Test the entire system flow from an end-user perspective. Interact with the application through its external interfaces (UI or API). Validate complete user journeys or business processes against real or near-real dependencies.
- **Tools:** {e.g., Playwright, Cypress, Selenium (for UI); Postman/Newman, K6 (for API)}
- **Environment:** {Run against deployed environments (e.g., Staging) or a locally composed setup (Docker Compose).}
- **Location:** {e.g., `test/e2e/`}
- **Expectations:** {Cover critical user paths. Slower, potentially flaky, run less frequently (e.g., pre-release, nightly).}

### Manual / Exploratory Testing (Optional)

- **Scope:** {Where is manual testing still required? e.g., Exploratory testing for usability, testing complex edge cases.}
- **Process:** {How is it performed and tracked?}

## Specialized Testing Types (Add sections as needed)

### Performance Testing

- **Scope & Goals:** {What needs performance testing? What are the targets (latency, throughput)?}
- **Tools:** {e.g., K6, JMeter, Locust}

### Security Testing

- **Scope & Goals:** {e.g., Dependency scanning, SAST, DAST, penetration testing requirements.}
- **Tools:** {e.g., Snyk, OWASP ZAP, Dependabot}

### Accessibility Testing (UI)

- **Scope & Goals:** {Target WCAG level, key areas.}
- **Tools:** {e.g., Axe, Lighthouse, manual checks}

### Visual Regression Testing (UI)

- **Scope & Goals:** {Prevent unintended visual changes.}
- **Tools:** {e.g., Percy, Applitools Eyes, Playwright visual comparisons}

## Test Data Management

{How is test data generated, managed, and reset for different testing levels?}

## CI/CD Integration

{How and when are tests executed in the CI/CD pipeline? What constitutes a pipeline failure?}

## Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |

```

## API Reference Template

```Markdown
# {Project Name} API Reference

## External APIs Consumed

{Repeat this section for each external API the system interacts with.}

### {External Service Name} API

- **Purpose:** {Why does the system use this API?}
- **Base URL(s):**
  - Production: `{URL}`
  - Staging/Dev: `{URL}`
- **Authentication:** {Describe method - e.g., API Key in Header (Header Name: `X-API-Key`), OAuth 2.0 Client Credentials, Basic Auth. Reference `docs/environment-vars.md` for key names.}
- **Key Endpoints Used:**
  - **`{HTTP Method} {/path/to/endpoint}`:**
    - Description: {What does this endpoint do?}
    - Request Parameters: {Query params, path params}
    - Request Body Schema: {Provide JSON schema or link to `docs/data-models.md`}
    - Example Request: `{Code block}`
    - Success Response Schema (Code: `200 OK`): {JSON schema or link}
    - Error Response Schema(s) (Codes: `4xx`, `5xx`): {JSON schema or link}
    - Example Response: `{Code block}`
  - **`{HTTP Method} {/another/endpoint}`:** {...}
- **Rate Limits:** {If known}
- **Link to Official Docs:** {URL}

### {Another External Service Name} API

{...}

## Internal APIs Provided (If Applicable)

{If the system exposes its own APIs (e.g., in a microservices architecture or for a UI frontend). Repeat for each API.}

### {Internal API / Service Name} API

- **Purpose:** {What service does this API provide?}
- **Base URL(s):** {e.g., `/api/v1/...`}
- **Authentication/Authorization:** {Describe how access is controlled.}
- **Endpoints:**
  - **`{HTTP Method} {/path/to/endpoint}`:**
    - Description: {What does this endpoint do?}
    - Request Parameters: {...}
    - Request Body Schema: {...}
    - Success Response Schema (Code: `200 OK`): {...}
    - Error Response Schema(s) (Codes: `4xx`, `5xx`): {...}
  - **`{HTTP Method} {/another/endpoint}`:** {...}

## AWS Service SDK Usage (or other Cloud Providers)

{Detail interactions with cloud provider services via SDKs.}

### {AWS Service Name, e.g., S3}

- **Purpose:** {Why is this service used?}
- **SDK Package:** {e.g., `@aws-sdk/client-s3`}
- **Key Operations Used:** {e.g., `GetObjectCommand`, `PutObjectCommand`}
  - Operation 1: {Brief description of usage context}
  - Operation 2: {...}
- **Key Resource Identifiers:** {e.g., Bucket names, Table names - reference `docs/environment-vars.md`}

### {Another AWS Service Name, e.g., SES}

{...}

## 5. Change Log

| Change        | Date       | Version | Description   | Author         |
| ------------- | ---------- | ------- | ------------- | -------------- |
| Initial draft | YYYY-MM-DD | 0.1     | Initial draft | {Agent/Person} |
| ...           | ...        | ...     | ...           | ...            |
```
