# Epic {N}: {Epic Title}

**Goal:** {State the overall goal this epic aims to achieve, linking back to the PRD goals.}

## Story List

{List all stories within this epic. Repeat the structure below for each story.}

### Story {N}.{M}: {Story Title}

- **User Story / Goal:** {Describe the story goal, ideally in "As a [role], I want [action], so that [benefit]" format, or clearly state the technical goal.}
- **Detailed Requirements:**
  - {Bulleted list explaining the specific functionalities, behaviors, or tasks required for this story.}
  - {Reference other documents for context if needed, e.g., "Handle data according to `docs/data-models.md#EntityName`".}
  - {Include any technical constraints or details identified during refinement - added by Architect/PM/Tech SM.}
- **Acceptance Criteria (ACs):**
  - AC1: {Specific, verifiable condition that must be met.}
  - AC2: {Another verifiable condition.}
  - ACN: {...}
- **Tasks (Optional Initial Breakdown):**
  - [ ] {High-level task 1}
  - [ ] {High-level task 2}

---

### Story {N}.{M+1}: {Story Title}

- **User Story / Goal:** {...}
- **Detailed Requirements:**
  - {...}
- **Acceptance Criteria (ACs):**
  - AC1: {...}
  - AC2: {...}
- **Tasks (Optional Initial Breakdown):**
  - [ ] {...}

---

{... Add more stories ...}

## Change Log

| Change        | Date       | Version | Description                    | Author         |
| ------------- | ---------- | ------- | ------------------------------ | -------------- |
