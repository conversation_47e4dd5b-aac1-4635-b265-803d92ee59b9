{Format output as markdown that follows}

# Project Brief: {Project Name}

## Introduction / Problem Statement

{Describe the core idea, the problem being solved, or the opportunity being addressed. Why is this project needed?}

## Vision & Goals

- **Vision:** {Describe the high-level desired future state or impact of this project.}
- **Primary Goals:** {List 2-5 specific, measurable, achievable, relevant, time-bound (SMART) goals for the Minimum Viable Product (MVP).}
  - Goal 1: ...
  - Goal 2: ...
- **Success Metrics (Initial Ideas):** {How will we measure if the project/MVP is successful? List potential KPIs.}

## Target Audience / Users

{Describe the primary users of this product/system. Who are they? What are their key characteristics or needs relevant to this project?}

## Key Features / Scope (High-Level Ideas for MVP)

{List the core functionalities or features envisioned for the MVP. Keep this high-level; details will go in the PRD/Epics.}

- Feature Idea 1: ...
- Feature Idea 2: ...
- Feature Idea N: ...

## Known Technical Constraints or Preferences

- **Constraints:** {List any known limitations and technical mandates or preferences - e.g., budget, timeline, specific technology mandates, required integrations, compliance needs.}
- **Risks:** {Identify potential risks - e.g., technical challenges, resource availability, market acceptance, dependencies.}

## Relevant Research (Optional)

{Link to or summarize findings from any initial research conducted and referenced.}

## PM Prompt

{The Prompt that will be used with the PM agent to initiate the PRD creation process}
